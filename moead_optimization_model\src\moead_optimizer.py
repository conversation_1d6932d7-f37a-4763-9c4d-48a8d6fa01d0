#!/usr/bin/env python3
"""
MOEA/D多目标优化算法实现

基于分解的多目标进化算法(Multi-Objective Evolutionary Algorithm based on Decomposition)
用于化工车间温度序列的多目标优化，同时优化：
1. label_1最小化（产品质量指标）
2. label_2最大化（产品质量指标）
3. 平滑性最大化（工艺稳定性指标）

主要特性：
- 基于Tchebycheff分解策略
- 差分进化变异操作
- 邻域更新机制
- 外部档案管理
- Pareto前沿维护
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Callable, Optional, Any
import logging
import yaml
import random
import copy
from datetime import datetime
from scipy.spatial.distance import euclidean
from scipy.interpolate import interp1d
import joblib
import os
try:
    from .training_data_constraints import TrainingDataConstraints
except ImportError:
    from training_data_constraints import TrainingDataConstraints

logger = logging.getLogger(__name__)


class Individual:
    """MOEA/D个体类"""

    def __init__(self, decision_variables: np.ndarray, objectives: Optional[np.ndarray] = None):
        """
        初始化个体

        Args:
            decision_variables: 决策变量（温度控制点）
            objectives: 目标函数值 [f1, f2, f3, f4]
        """
        self.decision_variables = decision_variables.copy()
        self.objectives = objectives.copy() if objectives is not None else None
        self.fitness = None  # 标量适应度值

    def copy(self):
        """创建个体副本"""
        return Individual(self.decision_variables, self.objectives)


class WeightVector:
    """权重向量类"""

    @staticmethod
    def generate_uniform_weights(num_objectives: int, population_size: int) -> np.ndarray:
        """
        生成均匀分布的权重向量

        Args:
            num_objectives: 目标函数数量
            population_size: 种群大小

        Returns:
            权重向量矩阵 [population_size, num_objectives]
        """
        if num_objectives == 2:
            # 二目标情况：线性分布
            weights = np.zeros((population_size, 2))
            for i in range(population_size):
                w1 = i / (population_size - 1)
                weights[i] = [w1, 1 - w1]
        elif num_objectives == 3:
            # 三目标情况：使用Das-Dennis方法
            weights = []
            H = int(np.sqrt(2 * population_size))  # 分割数

            for i in range(H + 1):
                for j in range(H + 1 - i):
                    k = H - i - j
                    if k >= 0:
                        w = np.array([i, j, k]) / H
                        weights.append(w)

            weights = np.array(weights)

            # 如果生成的权重向量数量不够，随机补充
            while len(weights) < population_size:
                w = np.random.random(3)
                w = w / np.sum(w)
                weights = np.vstack([weights, w])

            # 如果生成的权重向量数量过多，随机选择
            if len(weights) > population_size:
                indices = np.random.choice(len(weights), population_size, replace=False)
                weights = weights[indices]

        elif num_objectives == 4:
            # 四目标情况：使用改进的Das-Dennis方法
            weights = []
            H = max(3, int(np.power(population_size, 1/3)))  # 分割数

            for i in range(H + 1):
                for j in range(H + 1 - i):
                    for k in range(H + 1 - i - j):
                        l = H - i - j - k
                        if l >= 0:
                            w = np.array([i, j, k, l]) / H
                            weights.append(w)

            weights = np.array(weights)

            # 如果生成的权重向量数量不够，随机补充
            while len(weights) < population_size:
                w = np.random.random(4)
                w = w / np.sum(w)
                weights = np.vstack([weights, w])

            # 如果生成的权重向量数量过多，随机选择
            if len(weights) > population_size:
                indices = np.random.choice(len(weights), population_size, replace=False)
                weights = weights[indices]

        else:
            # 多目标情况：随机生成
            weights = np.random.random((population_size, num_objectives))
            weights = weights / np.sum(weights, axis=1, keepdims=True)

        return weights

    @staticmethod
    def find_neighbors(weights: np.ndarray, neighbor_size: int) -> List[List[int]]:
        """
        为每个权重向量找到最近的邻居

        Args:
            weights: 权重向量矩阵
            neighbor_size: 邻域大小

        Returns:
            每个权重向量的邻居索引列表
        """
        population_size = len(weights)
        neighbors = []

        for i in range(population_size):
            distances = []
            for j in range(population_size):
                if i != j:
                    dist = euclidean(weights[i], weights[j])
                    distances.append((dist, j))

            # 按距离排序，选择最近的邻居
            distances.sort()
            neighbor_indices = [i]  # 包含自己
            for k in range(min(neighbor_size - 1, len(distances))):
                neighbor_indices.append(distances[k][1])

            neighbors.append(neighbor_indices)

        return neighbors


class MOEADOptimizer:
    """MOEA/D多目标优化器"""

    def __init__(self, config_path: str = "config/config.yaml", sample_analyzer=None):
        """
        初始化MOEA/D优化器

        Args:
            config_path: 配置文件路径
            sample_analyzer: 样本数据分析器（用于样本驱动优化）
        """
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # MOEA/D参数
        moead_config = self.config.get('moead', {})
        self.population_size = moead_config.get('population_size', 100)
        self.max_generations = moead_config.get('max_generations', 200)
        self.neighbor_size = moead_config.get('neighbor_size', 20)
        self.F = moead_config.get('F', 0.5)  # 差分进化缩放因子
        self.CR = moead_config.get('CR', 0.9)  # 交叉概率

        # 分解策略
        decomp_config = moead_config.get('decomposition', {})
        self.decomposition_method = decomp_config.get('method', 'tchebycheff')

        # 收敛条件
        conv_config = moead_config.get('convergence', {})
        self.tolerance = float(conv_config.get('tolerance', 1e-6))
        self.patience = int(conv_config.get('patience', 50))

        # 外部档案
        archive_config = moead_config.get('external_archive', {})
        self.max_archive_size = archive_config.get('max_size', 200)
        self.archive_update_strategy = archive_config.get('update_strategy', 'non_dominated')

        # 温度序列参数
        temp_config = self.config.get('pso', {}).get('temperature_sequence', {})
        self.control_points = temp_config.get('control_points', 200)
        self.min_temperature = temp_config.get('min_temperature', 13.0)
        self.max_temperature = temp_config.get('max_temperature', 152.0)
        self.max_change_rate = temp_config.get('max_change_rate', 0.10)

        # 真实数据统计特性约束（基于分析结果修正）
        self.target_mean_temperature = temp_config.get('target_mean_temperature', 132.3)  # 修正为实际训练数据均值
        self.target_std_temperature = temp_config.get('target_std_temperature', 1.3)     # 修正为实际训练数据标准差
        self.distribution_weight = temp_config.get('temperature_distribution_weight', 0.5)  # 增加分布权重

        # 变长序列支持
        var_length_config = temp_config.get('variable_length', {})
        self.variable_length_enabled = var_length_config.get('enable', True)
        self.min_sequence_length = var_length_config.get('min_length', 18809)
        self.max_sequence_length = var_length_config.get('max_length', 92003)
        self.default_sequence_length = var_length_config.get('default_length', 24321)  # 修复：基于Sample_1.xlsx真实数据长度

        # 温度分布特性约束（基于训练数据分析修正）
        self.enforce_distribution_matching = var_length_config.get('enforce_distribution_matching', True)
        self.low_temp_ratio = var_length_config.get('low_temp_region_ratio', 0.15)      # 保持15%低温区域
        self.medium_temp_ratio = var_length_config.get('medium_temp_region_ratio', 0.25) # 保持25%中温区域
        self.high_temp_ratio = var_length_config.get('high_temp_region_ratio', 0.60)     # 保持60%高温区域

        # 多目标函数配置
        self.num_objectives = 4  # f1_label1, f2_label2, f3_smoothness, f4_distribution_matching
        multi_obj_config = self.config.get('multi_objective', {})
        self.objective_weights = [
            multi_obj_config.get('objective_weights', {}).get('f1_label1', 1.0),
            multi_obj_config.get('objective_weights', {}).get('f2_label2', 1.0),
            multi_obj_config.get('objective_weights', {}).get('f3_smoothness', 1.0),
            multi_obj_config.get('objective_weights', {}).get('f4_distribution_matching', 1.0)
        ]

        # 初始化组件
        self.population = []
        self.weight_vectors = None
        self.neighbors = None
        self.external_archive = []
        self.ideal_point = None
        self.nadir_point = None

        # 优化历史
        self.generation_history = []
        self.convergence_history = []
        self.hypervolume_history = []

        # 训练数据约束管理器
        constraint_file = "corrected_training_constraints.json"
        if not os.path.exists(constraint_file):
            constraint_file = "range_based_training_constraints.json"
        self.training_constraints = TrainingDataConstraints(constraint_file)
        self.constraint_strength = temp_config.get('constraint_strength', 0.9)  # 增强约束强度
        self.enable_constraints = temp_config.get('enable_training_constraints', True)

        if self.enable_constraints:
            logger.info("启用基于训练数据的约束机制")
            # 更新温度范围为训练数据实际范围
            bounds = self.training_constraints.constraints.get('temperature_bounds', {})
            if bounds:
                self.min_temperature = bounds.get('min', self.min_temperature)
                self.max_temperature = bounds.get('max', self.max_temperature)
                logger.info(f"更新温度范围为训练数据范围: [{self.min_temperature:.1f}, {self.max_temperature:.1f}]°C")

            # 更新目标统计特征
            stats = self.training_constraints.constraints.get('statistical_constraints', {})
            if stats:
                self.target_mean_temperature = stats.get('target_mean', self.target_mean_temperature)
                self.target_std_temperature = stats.get('target_std', self.target_std_temperature)
                logger.info(f"更新目标统计特征: 均值={self.target_mean_temperature:.1f}°C, 标准差={self.target_std_temperature:.1f}°C")

        # 目标函数
        self.objective_function = None

        logger.info(f"MOEA/D优化器初始化完成")
        logger.info(f"种群大小: {self.population_size}, 最大代数: {self.max_generations}")
        logger.info(f"邻域大小: {self.neighbor_size}, 分解方法: {self.decomposition_method}")

    def initialize_population(self) -> None:
        """初始化种群"""
        logger.info("初始化种群...")

        # 生成权重向量
        self.weight_vectors = WeightVector.generate_uniform_weights(
            self.num_objectives, self.population_size
        )

        # 找到邻居
        self.neighbors = WeightVector.find_neighbors(
            self.weight_vectors, self.neighbor_size
        )

        # 初始化种群
        self.population = []
        for i in range(self.population_size):
            # 基于真实训练数据生成起始温度分布的控制点
            if self.enable_constraints:
                control_points = self._generate_realistic_control_points()
            else:
                # 随机生成温度控制点
                control_points = np.random.uniform(
                    self.min_temperature,
                    self.max_temperature,
                    self.control_points
                )

            # 应用多维度约束
            control_points = self._apply_dimensional_constraints(control_points)

            # 创建个体
            individual = Individual(control_points)
            self.population.append(individual)

        # 评估初始种群
        self._evaluate_population()

        # 初始化理想点和最坏点
        self._update_reference_points()

        logger.info(f"种群初始化完成，包含 {len(self.population)} 个个体")

    def _apply_change_rate_constraint(self, control_points: np.ndarray) -> np.ndarray:
        """
        应用温度变化率约束（增强版，支持维度区域约束）

        Args:
            control_points: 温度控制点

        Returns:
            约束后的控制点
        """
        constrained_points = control_points.copy()

        for i in range(1, len(constrained_points)):
            # 基础变化率约束
            max_change = self.max_change_rate * (self.max_temperature - self.min_temperature)

            # 根据温度区域调整变化率约束
            current_temp = constrained_points[i-1]
            adjusted_max_change = self._get_zone_adjusted_change_rate(current_temp, max_change)

            # 限制变化幅度
            change = constrained_points[i] - constrained_points[i-1]
            if abs(change) > adjusted_max_change:
                if change > 0:
                    constrained_points[i] = constrained_points[i-1] + adjusted_max_change
                else:
                    constrained_points[i] = constrained_points[i-1] - adjusted_max_change

            # 确保在温度范围内
            constrained_points[i] = np.clip(
                constrained_points[i],
                self.min_temperature,
                self.max_temperature
            )

        return constrained_points

    def _get_zone_adjusted_change_rate(self, current_temp: float, base_max_change: float) -> float:
        """
        根据当前温度区域调整最大变化率

        Args:
            current_temp: 当前温度
            base_max_change: 基础最大变化率

        Returns:
            调整后的最大变化率
        """
        temp_range = self.max_temperature - self.min_temperature

        # 定义温度区域（基于数据分析）
        very_low_threshold = self.min_temperature + 0.2 * temp_range
        low_threshold = self.min_temperature + 0.4 * temp_range
        medium_threshold = self.min_temperature + 0.6 * temp_range
        high_threshold = self.min_temperature + 0.8 * temp_range

        # 根据温度区域调整变化率
        if current_temp < very_low_threshold:
            # 极低温区：允许较大变化率（快速升温）
            return base_max_change * 1.2
        elif current_temp < low_threshold:
            # 低温区：标准变化率
            return base_max_change
        elif current_temp < medium_threshold:
            # 中温区：稍微限制变化率
            return base_max_change * 0.8
        elif current_temp < high_threshold:
            # 高温区：更严格的变化率限制
            return base_max_change * 0.6
        else:
            # 极高温区：最严格的变化率限制
            return base_max_change * 0.4

    def _apply_temporal_constraints(self, control_points: np.ndarray) -> np.ndarray:
        """
        应用时间维度约束（开始、中间、结束阶段的不同约束）

        Args:
            control_points: 温度控制点

        Returns:
            应用时间约束后的控制点
        """
        constrained_points = control_points.copy()
        n_points = len(constrained_points)

        # 定义时间阶段
        begin_end = n_points // 3
        middle_start = begin_end
        middle_end = 2 * begin_end

        # 开始阶段约束（基于数据分析：通常从较低温度开始）
        for i in range(begin_end):
            # 开始阶段温度范围更严格
            phase_min = self.min_temperature
            phase_max = self.min_temperature + 0.6 * (self.max_temperature - self.min_temperature)
            constrained_points[i] = np.clip(constrained_points[i], phase_min, phase_max)

        # 中间阶段约束（允许更大的温度范围）
        for i in range(middle_start, middle_end):
            # 中间阶段可以达到更高温度
            phase_min = self.min_temperature + 0.2 * (self.max_temperature - self.min_temperature)
            phase_max = self.max_temperature
            constrained_points[i] = np.clip(constrained_points[i], phase_min, phase_max)

        # 结束阶段约束（通常维持在中高温）
        for i in range(middle_end, n_points):
            # 结束阶段温度范围
            phase_min = self.min_temperature + 0.3 * (self.max_temperature - self.min_temperature)
            phase_max = self.max_temperature
            constrained_points[i] = np.clip(constrained_points[i], phase_min, phase_max)

        return constrained_points

    def _apply_statistical_distribution_constraints(self, control_points: np.ndarray) -> np.ndarray:
        """
        应用统计分布约束，使生成的温度序列更接近真实数据的统计特性

        Args:
            control_points: 温度控制点

        Returns:
            应用统计分布约束后的控制点
        """
        if not self.enforce_distribution_matching:
            return control_points

        constrained_points = control_points.copy()
        n_points = len(constrained_points)

        # 1. 调整整体均值和标准差
        current_mean = np.mean(constrained_points)
        current_std = np.std(constrained_points)

        # 如果当前统计特性偏离目标太远，进行调整（放宽容忍度）
        mean_diff = self.target_mean_temperature - current_mean
        std_ratio = self.target_std_temperature / max(current_std, 1e-6)

        # 放宽均值容忍度到±10°C，标准差容忍度到±50%
        if abs(mean_diff) > 10.0 or abs(std_ratio - 1.0) > 0.5:
            # 温和调整：只调整50%的差异
            adjustment_factor = 0.5

            # 调整均值
            if abs(mean_diff) > 10.0:
                constrained_points += mean_diff * adjustment_factor

            # 调整标准差（如果差异过大）
            if abs(std_ratio - 1.0) > 0.5:
                current_std_new = np.std(constrained_points)
                if current_std_new > 1e-6:
                    target_std_adjusted = current_std_new + (self.target_std_temperature - current_std_new) * adjustment_factor
                    normalized_points = (constrained_points - np.mean(constrained_points)) / current_std_new
                    constrained_points = normalized_points * target_std_adjusted + np.mean(constrained_points)

            # 确保在合理范围内
            constrained_points = np.clip(constrained_points, self.min_temperature, self.max_temperature)

        # 2. 应用温度区域分布约束
        constrained_points = self._apply_temperature_region_constraints(constrained_points)

        return constrained_points

    def _apply_temperature_region_constraints(self, control_points: np.ndarray) -> np.ndarray:
        """
        应用温度区域分布约束，确保各温度区域的比例符合真实数据

        Args:
            control_points: 温度控制点

        Returns:
            应用区域约束后的控制点
        """
        constrained_points = control_points.copy()
        n_points = len(constrained_points)

        # 定义温度区域边界
        low_temp_boundary = 50.0
        medium_temp_boundary = 100.0

        # 计算当前各区域的点数
        low_temp_mask = constrained_points < low_temp_boundary
        medium_temp_mask = (constrained_points >= low_temp_boundary) & (constrained_points < medium_temp_boundary)
        high_temp_mask = constrained_points >= medium_temp_boundary

        current_low_ratio = np.sum(low_temp_mask) / n_points
        current_medium_ratio = np.sum(medium_temp_mask) / n_points
        current_high_ratio = np.sum(high_temp_mask) / n_points

        # 如果分布偏差较大，进行调整
        low_diff = abs(current_low_ratio - self.low_temp_ratio)
        medium_diff = abs(current_medium_ratio - self.medium_temp_ratio)
        high_diff = abs(current_high_ratio - self.high_temp_ratio)

        if max(low_diff, medium_diff, high_diff) > 0.1:  # 10%的容忍度
            # 重新分配温度点到各区域
            target_low_count = int(n_points * self.low_temp_ratio)
            target_medium_count = int(n_points * self.medium_temp_ratio)
            target_high_count = n_points - target_low_count - target_medium_count

            # 排序后重新分配
            sorted_indices = np.argsort(constrained_points)

            # 低温区域
            for i in range(target_low_count):
                idx = sorted_indices[i]
                if constrained_points[idx] >= low_temp_boundary:
                    constrained_points[idx] = np.random.uniform(
                        self.min_temperature,
                        low_temp_boundary - 1
                    )

            # 中温区域
            for i in range(target_low_count, target_low_count + target_medium_count):
                idx = sorted_indices[i]
                if constrained_points[idx] < low_temp_boundary or constrained_points[idx] >= medium_temp_boundary:
                    constrained_points[idx] = np.random.uniform(
                        low_temp_boundary,
                        medium_temp_boundary - 1
                    )

            # 高温区域
            for i in range(target_low_count + target_medium_count, n_points):
                idx = sorted_indices[i]
                if constrained_points[idx] < medium_temp_boundary:
                    constrained_points[idx] = np.random.uniform(
                        medium_temp_boundary,
                        self.max_temperature
                    )

        return constrained_points

    def _apply_window_constraints(self, control_points: np.ndarray, window_size: int = 5) -> np.ndarray:
        """
        应用局部窗口约束

        Args:
            control_points: 温度控制点
            window_size: 窗口大小

        Returns:
            应用窗口约束后的控制点
        """
        constrained_points = control_points.copy()
        n_points = len(constrained_points)

        # 滑动窗口约束
        for i in range(n_points - window_size + 1):
            window = constrained_points[i:i+window_size]

            # 限制窗口内的温度范围（基于数据分析）
            max_window_range = 0.15 * (self.max_temperature - self.min_temperature)  # 15%的总范围
            current_range = np.max(window) - np.min(window)

            if current_range > max_window_range:
                # 压缩窗口内的温度范围
                window_mean = np.mean(window)
                half_range = max_window_range / 2

                for j in range(window_size):
                    if window[j] > window_mean + half_range:
                        constrained_points[i+j] = window_mean + half_range
                    elif window[j] < window_mean - half_range:
                        constrained_points[i+j] = window_mean - half_range

        return constrained_points

    def _apply_dimensional_constraints(self, control_points: np.ndarray) -> np.ndarray:
        """
        应用所有维度约束的综合方法

        Args:
            control_points: 温度控制点

        Returns:
            应用所有约束后的控制点
        """
        constrained_points = control_points.copy()

        # 1. 应用基础变化率约束
        constrained_points = self._apply_change_rate_constraint(constrained_points)

        # 2. 应用时间维度约束
        constrained_points = self._apply_temporal_constraints(constrained_points)

        # 3. 应用统计分布约束（新增）
        constrained_points = self._apply_statistical_distribution_constraints(constrained_points)

        # 4. 应用训练数据约束（新增）
        if self.enable_constraints:
            # 生成完整温度序列以应用约束
            temp_sequence = self._generate_temperature_sequence(constrained_points)
            # 应用训练数据约束
            constrained_sequence = self.training_constraints.apply_comprehensive_constraints(
                temp_sequence, constraint_strength=self.constraint_strength
            )
            # 从约束后的序列重新提取控制点
            constrained_points = self._extract_control_points_from_sequence(constrained_sequence)

        # 5. 应用局部窗口约束
        constrained_points = self._apply_window_constraints(constrained_points)

        # 6. 最终边界检查
        constrained_points = np.clip(
            constrained_points,
            self.min_temperature,
            self.max_temperature
        )

        return constrained_points

    def _extract_control_points_from_sequence(self, temperature_sequence: np.ndarray) -> np.ndarray:
        """
        从完整温度序列中提取控制点

        Args:
            temperature_sequence: 完整温度序列

        Returns:
            提取的控制点
        """
        # 均匀采样控制点
        sequence_length = len(temperature_sequence)
        control_indices = np.linspace(0, sequence_length - 1, self.control_points, dtype=int)
        control_points = temperature_sequence[control_indices]

        return control_points

    def _generate_realistic_control_points(self) -> np.ndarray:
        """
        基于范围约束生成控制点，允许优化算法在合理范围内自由生成温度值

        Returns:
            符合训练数据范围约束的控制点
        """
        if not hasattr(self, 'training_constraints') or not self.training_constraints.constraints:
            # 如果没有约束数据，使用默认方法
            return np.random.uniform(self.min_temperature, self.max_temperature, self.control_points)

        # 获取范围约束
        starting_constraints = self.training_constraints.constraints.get('starting_temperature_constraints', {})
        progression = self.training_constraints.constraints.get('temperature_progression', {})
        phase_constraints = self.training_constraints.constraints.get('phase_constraints', {})
        
        # 起始温度范围
        start_range = starting_constraints.get('allowed_range', [13.5, 102.1])
        typical_start_range = starting_constraints.get('typical_range', [16.0, 52.0])
        
        # 结束温度范围
        end_range = progression.get('end_range', [130.0, 150.0])
        
        # 生成控制点
        control_points = np.zeros(self.control_points)
        
        # 第一个点：严格按照训练数据分布生成起始温度
        # 85.7%概率在50°C以下，28.6%概率在20°C以下
        rand_val = np.random.random()
        if rand_val < 0.286:  # 28.6%概率在20°C以下
            control_points[0] = np.random.uniform(13.5, 20.0)
        elif rand_val < 0.857:  # 额外57.1%概率在20-50°C之间
            control_points[0] = np.random.uniform(20.0, 50.0)
        else:  # 14.3%概率在50°C以上
            control_points[0] = np.random.uniform(50.0, min(102.1, typical_start_range[1]))
        
        # 最后一个点：在结束温度范围内随机生成（基于训练数据：129.0-146.6°C）
        end_temp = np.random.uniform(129.0, 146.6)
        
        # 生成中间控制点，允许在合理范围内变化
        for i in range(1, self.control_points):
            progress = i / (self.control_points - 1)  # 进度 0-1
            
            # 基础插值温度
            base_temp = control_points[0] + (end_temp - control_points[0]) * progress
            
            # 根据进度确定允许的变异范围
            if progress < 0.25:  # 早期阶段
                phase_range = phase_constraints.get('early', {}).get('temperature_range', [100, 140])
                variation = np.random.uniform(-5, 10)  # 允许更大的上升变异
            elif progress < 0.75:  # 中期阶段
                phase_range = phase_constraints.get('middle', {}).get('temperature_range', [130, 140])
                variation = np.random.uniform(-3, 5)   # 中等变异
            else:  # 后期阶段
                phase_range = phase_constraints.get('late', {}).get('temperature_range', [130, 145])
                variation = np.random.uniform(-2, 3)   # 较小变异
            
            # 应用变异
            control_points[i] = base_temp + variation
            
            # 确保在阶段范围内
            control_points[i] = np.clip(control_points[i], phase_range[0], phase_range[1])
            
            # 限制相邻点之间的变化
            max_decrease = progression.get('max_decrease_per_step', 2.0)
            max_increase = progression.get('max_increase_per_step', 5.0)
            
            if i > 0:
                change = control_points[i] - control_points[i-1]
                if change < -max_decrease:
                    control_points[i] = control_points[i-1] - max_decrease
                elif change > max_increase:
                    control_points[i] = control_points[i-1] + max_increase
        
        # 确保在全局温度边界内
        control_points = np.clip(control_points, self.min_temperature, self.max_temperature)
        
        # 软约束检查：如果整体统计特性偏差太大，进行温和调整
        stats_constraints = self.training_constraints.constraints.get('statistical_constraints', {})
        if not stats_constraints.get('enforce_exact_match', False):
            current_mean = np.mean(control_points)
            mean_range = stats_constraints.get('mean_range', [129.0, 136.0])
            
            # 如果平均温度超出合理范围，进行温和调整
            if current_mean < mean_range[0]:
                adjustment = (mean_range[0] - current_mean) * 0.3  # 温和调整
                control_points += adjustment
            elif current_mean > mean_range[1]:
                adjustment = (mean_range[1] - current_mean) * 0.3  # 温和调整
                control_points += adjustment
            
            # 再次确保边界
            control_points = np.clip(control_points, self.min_temperature, self.max_temperature)

        return control_points

    def validate_population_constraints(self) -> Dict[str, Any]:
        """
        验证当前种群是否满足训练数据约束

        Returns:
            约束验证结果统计
        """
        if not self.enable_constraints:
            return {'constraints_enabled': False}

        validation_results = {
            'constraints_enabled': True,
            'population_size': len(self.population),
            'valid_individuals': 0,
            'constraint_violations': {
                'statistical': 0,
                'gradient': 0,
                'smoothness': 0,
                'phase': 0
            },
            'statistics_summary': {
                'mean_temperatures': [],
                'std_temperatures': [],
                'max_gradients': [],
                'smoothness_scores': []
            }
        }

        for individual in self.population:
            # 生成完整温度序列
            temp_sequence = self._generate_temperature_sequence(individual.decision_variables)

            # 验证约束
            validation = self.training_constraints.validate_sequence(temp_sequence)

            if validation['overall_valid']:
                validation_results['valid_individuals'] += 1

            # 统计违反情况
            if not validation['statistical']['mean_valid'] or not validation['statistical']['std_valid']:
                validation_results['constraint_violations']['statistical'] += 1

            if not validation['gradient']['max_gradient_valid'] or not validation['gradient']['mean_gradient_valid']:
                validation_results['constraint_violations']['gradient'] += 1

            if not validation['smoothness']['smoothness_valid']:
                validation_results['constraint_violations']['smoothness'] += 1

            # 收集统计信息
            validation_results['statistics_summary']['mean_temperatures'].append(validation['statistical']['seq_mean'])
            validation_results['statistics_summary']['std_temperatures'].append(validation['statistical']['seq_std'])
            validation_results['statistics_summary']['max_gradients'].append(validation['gradient']['max_gradient'])
            validation_results['statistics_summary']['smoothness_scores'].append(validation['smoothness']['smoothness_score'])

        # 计算统计摘要
        for key, values in validation_results['statistics_summary'].items():
            if values:
                validation_results['statistics_summary'][key] = {
                    'mean': float(np.mean(values)),
                    'std': float(np.std(values)),
                    'min': float(np.min(values)),
                    'max': float(np.max(values))
                }

        # 计算有效率
        validation_results['validity_rate'] = validation_results['valid_individuals'] / validation_results['population_size']

        return validation_results

    def _evaluate_population(self) -> None:
        """评估整个种群的目标函数值"""
        for individual in self.population:
            if individual.objectives is None:
                individual.objectives = self._evaluate_individual(individual)

    def _evaluate_individual(self, individual: Individual) -> np.ndarray:
        """
        评估单个个体的目标函数值

        Args:
            individual: 待评估的个体

        Returns:
            目标函数值数组 [f1, f2, f3, f4]
        """
        if self.objective_function is None:
            raise ValueError("目标函数未设置，请先调用set_objective_function")

        # 生成完整的温度序列
        temperature_sequence = self._generate_temperature_sequence(individual.decision_variables)

        # 计算目标函数值
        objectives = self.objective_function(temperature_sequence)

        return np.array(objectives)

    def _generate_temperature_sequence(self, control_points: np.ndarray) -> np.ndarray:
        """
        从控制点生成完整的温度序列

        Args:
            control_points: 温度控制点

        Returns:
            完整的温度序列
        """
        # 验证输入
        if control_points is None or len(control_points) == 0:
            raise ValueError("控制点数组不能为空")

        # 确保control_points是一维数组
        control_points = np.asarray(control_points).flatten()

        # 如果只有一个控制点，生成常数序列
        if len(control_points) == 1:
            sequence_length = getattr(self, 'default_sequence_length', 1000)
            return np.full(sequence_length, control_points[0])

        # 使用基于数据分析的序列长度
        if hasattr(self, 'variable_length_enabled') and self.variable_length_enabled:
            sequence_length = getattr(self, 'default_sequence_length', 1000)
        else:
            sequence_length = 1000  # 向后兼容

        # 控制点的时间索引
        control_indices = np.linspace(0, sequence_length - 1, len(control_points))

        # 根据控制点数量选择插值方法
        if len(control_points) < 4:
            # 控制点少于4个时使用线性插值
            interpolation_kind = 'linear'
        else:
            # 控制点足够时使用三次样条插值
            interpolation_kind = 'cubic'

        # 插值生成完整序列
        try:
            interpolator = interp1d(
                control_indices,
                control_points,
                kind=interpolation_kind,
                bounds_error=False,
                fill_value='extrapolate'
            )
        except ValueError as e:
            # 如果插值失败，回退到线性插值
            print(f"警告: 三次插值失败，回退到线性插值: {e}")
            interpolator = interp1d(
                control_indices,
                control_points,
                kind='linear',
                bounds_error=False,
                fill_value='extrapolate'
            )

        # 生成完整时间序列
        time_indices = np.arange(sequence_length)
        temperature_sequence = interpolator(time_indices)

        # 确保温度在合理范围内
        temperature_sequence = np.clip(
            temperature_sequence,
            self.min_temperature,
            self.max_temperature
        )

        # 应用真实数据约束修正
        try:
            from src.real_data_constraints import RealDataConstraints
            constraints = RealDataConstraints()
            temperature_sequence = constraints.apply_constraints(temperature_sequence)
        except Exception as e:
            # 如果约束应用失败，记录警告但继续执行
            import logging
            logging.warning(f"约束应用失败: {e}")

        return temperature_sequence

    def _update_reference_points(self) -> None:
        """更新理想点和最坏点"""
        if not self.population:
            return

        # 收集所有目标函数值
        all_objectives = np.array([ind.objectives for ind in self.population if ind.objectives is not None])

        if len(all_objectives) == 0:
            return

        # 更新理想点（每个目标的最优值）
        if self.ideal_point is None:
            self.ideal_point = np.min(all_objectives, axis=0)
        else:
            self.ideal_point = np.minimum(self.ideal_point, np.min(all_objectives, axis=0))

        # 更新最坏点（每个目标的最差值）
        if self.nadir_point is None:
            self.nadir_point = np.max(all_objectives, axis=0)
        else:
            self.nadir_point = np.maximum(self.nadir_point, np.max(all_objectives, axis=0))

    def _tchebycheff_decomposition(self, objectives: np.ndarray, weight: np.ndarray) -> float:
        """
        Tchebycheff分解函数

        Args:
            objectives: 目标函数值
            weight: 权重向量

        Returns:
            分解后的标量适应度值
        """
        if self.ideal_point is None:
            return float('inf')

        # 标准化目标函数值
        normalized_objectives = objectives - self.ideal_point

        # Tchebycheff分解
        weighted_objectives = weight * normalized_objectives
        fitness = np.max(weighted_objectives)

        return fitness

    def _weighted_sum_decomposition(self, objectives: np.ndarray, weight: np.ndarray) -> float:
        """
        加权和分解函数

        Args:
            objectives: 目标函数值
            weight: 权重向量

        Returns:
            分解后的标量适应度值
        """
        if self.ideal_point is None:
            return float('inf')

        # 标准化目标函数值
        normalized_objectives = objectives - self.ideal_point

        # 加权和分解
        fitness = np.sum(weight * normalized_objectives)

        return fitness

    def _calculate_fitness(self, individual: Individual, weight_index: int) -> float:
        """
        计算个体在指定权重向量下的适应度

        Args:
            individual: 个体
            weight_index: 权重向量索引

        Returns:
            适应度值
        """
        if individual.objectives is None:
            individual.objectives = self._evaluate_individual(individual)

        weight = self.weight_vectors[weight_index]

        if self.decomposition_method == 'tchebycheff':
            return self._tchebycheff_decomposition(individual.objectives, weight)
        elif self.decomposition_method == 'weighted_sum':
            return self._weighted_sum_decomposition(individual.objectives, weight)
        else:
            raise ValueError(f"不支持的分解方法: {self.decomposition_method}")

    def set_objective_function(self, objective_function: Callable[[np.ndarray], List[float]]) -> None:
        """
        设置目标函数

        Args:
            objective_function: 目标函数，输入温度序列，返回[f1, f2, f3, f4]
        """
        self.objective_function = objective_function
        logger.info("目标函数已设置")

    def _differential_evolution_mutation(self, target_index: int) -> np.ndarray:
        """
        差分进化变异操作

        Args:
            target_index: 目标个体索引

        Returns:
            变异后的决策变量
        """
        # 从邻域中随机选择三个不同的个体
        neighbors = self.neighbors[target_index]

        if len(neighbors) < 3:
            # 如果邻域太小，从整个种群中选择
            available_indices = list(range(self.population_size))
            available_indices.remove(target_index)
            selected_indices = random.sample(available_indices, min(3, len(available_indices)))
        else:
            selected_indices = random.sample(neighbors[1:], min(3, len(neighbors) - 1))

        # 确保有足够的个体进行变异
        while len(selected_indices) < 3:
            candidate = random.randint(0, self.population_size - 1)
            if candidate != target_index and candidate not in selected_indices:
                selected_indices.append(candidate)

        # 差分进化变异：v = x1 + F * (x2 - x3)
        x1 = self.population[selected_indices[0]].decision_variables
        x2 = self.population[selected_indices[1]].decision_variables
        x3 = self.population[selected_indices[2]].decision_variables

        mutant = x1 + self.F * (x2 - x3)

        # 边界处理
        mutant = np.clip(mutant, self.min_temperature, self.max_temperature)

        # 应用训练数据约束
        if self.enable_constraints:
            mutant = self._apply_dimensional_constraints(mutant)

        return mutant

    def _crossover(self, target: np.ndarray, mutant: np.ndarray) -> np.ndarray:
        """
        交叉操作

        Args:
            target: 目标个体的决策变量
            mutant: 变异个体的决策变量

        Returns:
            交叉后的决策变量
        """
        offspring = target.copy()

        # 确保至少有一个基因来自变异个体
        random_index = random.randint(0, len(target) - 1)

        for i in range(len(target)):
            if random.random() < self.CR or i == random_index:
                offspring[i] = mutant[i]

        # 应用多维度约束
        offspring = self._apply_dimensional_constraints(offspring)

        return offspring

    def _update_neighbors(self, offspring: Individual, target_index: int) -> None:
        """
        更新邻域中的个体

        Args:
            offspring: 新生成的个体
            target_index: 目标个体索引
        """
        # 评估新个体
        if offspring.objectives is None:
            offspring.objectives = self._evaluate_individual(offspring)

        # 更新邻域中的个体
        neighbors = self.neighbors[target_index]

        for neighbor_index in neighbors:
            # 计算当前个体和新个体的适应度
            current_fitness = self._calculate_fitness(self.population[neighbor_index], neighbor_index)
            offspring_fitness = self._calculate_fitness(offspring, neighbor_index)

            # 如果新个体更好，则替换
            if offspring_fitness < current_fitness:
                self.population[neighbor_index] = offspring.copy()

    def _update_external_archive(self, individual: Individual) -> None:
        """
        更新外部档案

        Args:
            individual: 待添加的个体
        """
        # 检查是否被档案中的个体支配
        dominated = False
        for archive_individual in self.external_archive:
            if self._dominates(archive_individual.objectives, individual.objectives):
                dominated = True
                break

        if not dominated:
            # 移除被新个体支配的档案个体
            self.external_archive = [
                arch_ind for arch_ind in self.external_archive
                if not self._dominates(individual.objectives, arch_ind.objectives)
            ]

            # 添加新个体
            self.external_archive.append(individual.copy())

            # 如果档案过大，使用拥挤距离选择
            if len(self.external_archive) > self.max_archive_size:
                self._maintain_archive_size()

    def _dominates(self, obj1: np.ndarray, obj2: np.ndarray) -> bool:
        """
        检查obj1是否支配obj2（Pareto支配关系）

        Args:
            obj1: 目标函数值1
            obj2: 目标函数值2

        Returns:
            True如果obj1支配obj2
        """
        # 对于最小化问题，obj1支配obj2当且仅当：
        # 1. obj1在所有目标上都不差于obj2
        # 2. obj1在至少一个目标上严格优于obj2

        # 注意：f2、f3、f4需要最大化，所以要取负值比较
        obj1_modified = obj1.copy()
        obj2_modified = obj2.copy()

        # f1: 最小化，保持不变
        # f2: 最大化转为最小化
        if len(obj1_modified) > 1:
            obj1_modified[1] = -obj1_modified[1]
            obj2_modified[1] = -obj2_modified[1]
        # f3: 最大化转为最小化
        if len(obj1_modified) > 2:
            obj1_modified[2] = -obj1_modified[2]
            obj2_modified[2] = -obj2_modified[2]
        # f4: 最大化转为最小化
        if len(obj1_modified) > 3:
            obj1_modified[3] = -obj1_modified[3]
            obj2_modified[3] = -obj2_modified[3]

        better_or_equal = np.all(obj1_modified <= obj2_modified)
        strictly_better = np.any(obj1_modified < obj2_modified)

        return better_or_equal and strictly_better

    def _maintain_archive_size(self) -> None:
        """维护档案大小，使用拥挤距离选择"""
        if len(self.external_archive) <= self.max_archive_size:
            return

        # 计算拥挤距离
        objectives_matrix = np.array([ind.objectives for ind in self.external_archive])
        crowding_distances = self._calculate_crowding_distance(objectives_matrix)

        # 按拥挤距离排序，保留距离大的个体
        sorted_indices = np.argsort(crowding_distances)[::-1]
        selected_indices = sorted_indices[:self.max_archive_size]

        self.external_archive = [self.external_archive[i] for i in selected_indices]

    def _calculate_crowding_distance(self, objectives_matrix: np.ndarray) -> np.ndarray:
        """
        计算拥挤距离

        Args:
            objectives_matrix: 目标函数值矩阵 [n_individuals, n_objectives]

        Returns:
            拥挤距离数组
        """
        n_individuals, n_objectives = objectives_matrix.shape
        crowding_distances = np.zeros(n_individuals)

        for obj_idx in range(n_objectives):
            # 按当前目标排序
            sorted_indices = np.argsort(objectives_matrix[:, obj_idx])

            # 边界个体设置为无穷大
            crowding_distances[sorted_indices[0]] = float('inf')
            crowding_distances[sorted_indices[-1]] = float('inf')

            # 计算目标范围
            obj_range = objectives_matrix[sorted_indices[-1], obj_idx] - objectives_matrix[sorted_indices[0], obj_idx]

            if obj_range > 0:
                # 计算中间个体的拥挤距离
                for i in range(1, n_individuals - 1):
                    distance = (objectives_matrix[sorted_indices[i+1], obj_idx] -
                               objectives_matrix[sorted_indices[i-1], obj_idx]) / obj_range
                    crowding_distances[sorted_indices[i]] += distance

        return crowding_distances

    def optimize(self, objective_function: Callable[[np.ndarray], List[float]]) -> Dict[str, Any]:
        """
        执行MOEA/D优化

        Args:
            objective_function: 目标函数，输入温度序列，返回[f1, f2, f3, f4]

        Returns:
            优化结果字典
        """
        logger.info("开始MOEA/D多目标优化...")
        print(f"\n[MOEA/D优化] 开始优化过程...")
        print(f"[MOEA/D优化] 种群大小: {self.population_size}, 最大代数: {self.max_generations}")
        print(f"[MOEA/D优化] 目标函数数量: {self.num_objectives}")
        print("-" * 80)

        # 设置目标函数
        self.set_objective_function(objective_function)

        # 初始化种群
        self.initialize_population()

        # 记录开始时间
        start_time = datetime.now()

        # 收敛检查
        no_improvement_count = 0
        best_hypervolume = 0

        # 主优化循环
        for generation in range(self.max_generations):
            generation_start_time = datetime.now()

            # 对每个个体进行进化
            for i in range(self.population_size):
                # 差分进化变异
                mutant = self._differential_evolution_mutation(i)

                # 交叉操作
                offspring_vars = self._crossover(self.population[i].decision_variables, mutant)

                # 创建新个体
                offspring = Individual(offspring_vars)

                # 更新邻域
                self._update_neighbors(offspring, i)

                # 更新外部档案
                self._update_external_archive(offspring)

            # 更新参考点
            self._update_reference_points()

            # 计算性能指标
            current_hypervolume = self._calculate_hypervolume()
            self.hypervolume_history.append(current_hypervolume)

            # 收敛检查
            if current_hypervolume > best_hypervolume + self.tolerance:
                best_hypervolume = current_hypervolume
                no_improvement_count = 0
            else:
                no_improvement_count += 1

            # 记录历史
            generation_time = (datetime.now() - generation_start_time).total_seconds()

            # 约束验证（每10代验证一次）
            constraint_validation = None
            if self.enable_constraints and generation % 10 == 0:
                constraint_validation = self.validate_population_constraints()

            self.generation_history.append({
                'generation': generation,
                'hypervolume': current_hypervolume,
                'archive_size': len(self.external_archive),
                'time': generation_time,
                'constraint_validation': constraint_validation
            })

            # 输出进度
            if generation % 10 == 0 or generation == self.max_generations - 1:
                progress_msg = (f"[MOEA/D优化] 代数: {generation:3d}/{self.max_generations}, "
                              f"超体积: {current_hypervolume:.6f}, "
                              f"档案大小: {len(self.external_archive):3d}, "
                              f"用时: {generation_time:.2f}s")

                # 添加约束信息
                if constraint_validation and constraint_validation['constraints_enabled']:
                    validity_rate = constraint_validation['validity_rate']
                    mean_temp = constraint_validation['statistics_summary']['mean_temperatures']['mean']
                    progress_msg += f", 约束满足率: {validity_rate:.1%}, 平均温度: {mean_temp:.1f}°C"

                print(progress_msg)

            # 早停检查
            if no_improvement_count >= self.patience:
                logger.info(f"在第{generation}代达到收敛条件，提前停止")
                print(f"[MOEA/D优化] 在第{generation}代达到收敛条件，提前停止")
                break

        # 计算总时间
        total_time = (datetime.now() - start_time).total_seconds()

        # 提取Pareto前沿
        pareto_front = self._extract_pareto_front()

        # 最终约束验证
        final_constraint_validation = None
        if self.enable_constraints:
            final_constraint_validation = self.validate_population_constraints()
            logger.info(f"最终约束满足率: {final_constraint_validation['validity_rate']:.1%}")

        # 构建结果
        results = {
            'pareto_front': pareto_front,
            'external_archive': self.external_archive,
            'final_population': self.population,
            'total_generations': generation + 1,
            'total_time': total_time,
            'converged': no_improvement_count >= self.patience,
            'final_hypervolume': current_hypervolume,
            'hypervolume_history': self.hypervolume_history,
            'generation_history': self.generation_history,
            'ideal_point': self.ideal_point,
            'nadir_point': self.nadir_point,
            'weight_vectors': self.weight_vectors,
            'constraint_validation': final_constraint_validation,
            'algorithm_config': {
                'population_size': self.population_size,
                'max_generations': self.max_generations,
                'neighbor_size': self.neighbor_size,
                'F': self.F,
                'CR': self.CR,
                'decomposition_method': self.decomposition_method,
                'constraints_enabled': self.enable_constraints,
                'constraint_strength': self.constraint_strength
            }
        }

        logger.info("MOEA/D优化完成！")
        logger.info(f"总代数: {generation + 1}")
        logger.info(f"最终超体积: {current_hypervolume:.6f}")
        logger.info(f"Pareto前沿解数量: {len(pareto_front)}")
        logger.info(f"总用时: {total_time:.2f}秒")

        print(f"\n[MOEA/D优化] 优化完成！")
        print(f"[MOEA/D优化] 总代数: {generation + 1}")
        print(f"[MOEA/D优化] 最终超体积: {current_hypervolume:.6f}")
        print(f"[MOEA/D优化] Pareto前沿解数量: {len(pareto_front)}")
        print(f"[MOEA/D优化] 总用时: {total_time:.2f}秒")

        return results

    def _calculate_hypervolume(self) -> float:
        """
        计算超体积指标（简化版本）

        Returns:
            超体积值
        """
        if not self.external_archive:
            return 0.0

        # 提取目标函数值
        objectives_matrix = np.array([ind.objectives for ind in self.external_archive])

        # 简化的超体积计算：使用档案大小和目标函数值的分布
        # 这里使用一个简化的指标来代替真正的超体积计算

        # 计算目标空间的覆盖范围
        if self.ideal_point is not None and self.nadir_point is not None:
            # 标准化目标函数值
            normalized_objectives = (objectives_matrix - self.ideal_point) / (self.nadir_point - self.ideal_point + 1e-10)

            # 计算覆盖的超体积（简化版）
            volume = len(self.external_archive) * np.mean(np.prod(1 - normalized_objectives, axis=1))
        else:
            volume = len(self.external_archive)

        return volume

    def _extract_pareto_front(self) -> List[Dict[str, Any]]:
        """
        提取Pareto前沿解

        Returns:
            Pareto前沿解列表
        """
        pareto_front = []

        for individual in self.external_archive:
            # 生成完整温度序列
            temperature_sequence = self._generate_temperature_sequence(individual.decision_variables)

            solution = {
                'decision_variables': individual.decision_variables.copy(),
                'temperature_sequence': temperature_sequence,
                'objectives': individual.objectives.copy(),
                'f1_label1': individual.objectives[0],
                'f2_label2': individual.objectives[1],
                'f3_smoothness': individual.objectives[2],
                'f4_distribution': individual.objectives[3] if len(individual.objectives) > 3 else 0.0
            }
            pareto_front.append(solution)

        # 按第一个目标函数排序
        pareto_front.sort(key=lambda x: x['f1_label1'])

        return pareto_front

    def get_best_solutions(self, n_solutions: int = 5) -> List[Dict[str, Any]]:
        """
        获取最佳解决方案

        Args:
            n_solutions: 返回的解决方案数量

        Returns:
            最佳解决方案列表
        """
        if not self.external_archive:
            return []

        pareto_front = self._extract_pareto_front()

        # 返回前n个解
        return pareto_front[:min(n_solutions, len(pareto_front))]

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取优化统计信息

        Returns:
            统计信息字典
        """
        if not self.generation_history:
            return {}

        hypervolumes = [gen['hypervolume'] for gen in self.generation_history]
        times = [gen['time'] for gen in self.generation_history]

        stats = {
            'total_generations': len(self.generation_history),
            'final_hypervolume': hypervolumes[-1] if hypervolumes else 0,
            'max_hypervolume': max(hypervolumes) if hypervolumes else 0,
            'hypervolume_improvement': hypervolumes[-1] - hypervolumes[0] if len(hypervolumes) > 1 else 0,
            'average_generation_time': np.mean(times) if times else 0,
            'total_time': sum(times) if times else 0,
            'pareto_front_size': len(self.external_archive),
            'convergence_generation': None
        }

        # 找到收敛代数
        if len(hypervolumes) > 1:
            for i in range(1, len(hypervolumes)):
                if abs(hypervolumes[i] - hypervolumes[i-1]) < self.tolerance:
                    stats['convergence_generation'] = i
                    break

        return stats


def main():
    """测试MOEA/D优化器功能"""
    # 简单的测试目标函数
    def test_multi_objective_function(temperature_sequence):
        """
        测试用的多目标函数

        Args:
            temperature_sequence: 温度序列

        Returns:
            [f1, f2, f3, f4] - [最小化, 最大化, 最大化, 最大化]
        """
        # f1: 最小化温度方差（稳定性）
        f1 = np.var(temperature_sequence)

        # f2: 最大化平均温度（效率）
        f2 = np.mean(temperature_sequence)

        # f3: 最大化平滑性（工艺稳定性）
        diff = np.diff(temperature_sequence)
        f3 = -np.mean(np.abs(diff))  # 负值因为要最大化平滑性

        # f4: 最大化分布匹配度（简单测试函数）
        f4 = -np.std(temperature_sequence)  # 负值因为要最大化一致性

        return [f1, f2, f3, f4]

    # 创建MOEA/D优化器
    optimizer = MOEADOptimizer()

    # 执行优化
    results = optimizer.optimize(test_multi_objective_function)

    print(f"\n优化完成！")
    print(f"Pareto前沿解数量: {len(results['pareto_front'])}")
    print(f"最终超体积: {results['final_hypervolume']:.6f}")

    # 显示最佳解决方案
    best_solutions = optimizer.get_best_solutions(3)
    for i, solution in enumerate(best_solutions):
        print(f"\n解决方案 {i+1}:")
        print(f"  f1 (最小化): {solution['f1_label1']:.4f}")
        print(f"  f2 (最大化): {solution['f2_label2']:.4f}")
        print(f"  f3 (最大化): {solution['f3_smoothness']:.4f}")
        print(f"  f4 (最大化): {solution['f4_distribution']:.4f}")


if __name__ == "__main__":
    main()