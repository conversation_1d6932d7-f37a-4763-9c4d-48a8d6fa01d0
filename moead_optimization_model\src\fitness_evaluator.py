#!/usr/bin/env python3
"""
适应度评估器模块

该模块负责：
1. 基于训练好的分类器评估温度序列质量
2. 提供多种适应度计算策略
3. 处理序列质量的相对比较
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Callable, Optional, Tuple, Any
import logging
import yaml
import random
from scipy import fft
from scipy.interpolate import interp1d
import joblib
import os

logger = logging.getLogger(__name__)


class ClassificationBasedFitnessEvaluator:
    """基于分类学习的适应度评估器（用于MOEA/D多目标优化）"""

    def __init__(self, classifier=None, feature_extractor=None,
                 config_path: str = "config/config.yaml"):
        """
        初始化基于分类学习的适应度评估器

        Args:
            classifier: 训练好的分类器（可选，支持延迟加载）
            feature_extractor: 特征提取器（可选，支持延迟加载）
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        self.classifier = classifier
        self.feature_extractor = feature_extractor

        # 评估配置
        self.evaluation_config = self.config.get('classification_fitness', {})
        self.use_pareto_classification = self.evaluation_config.get('use_pareto_classification', True)
        self.objective_weights = self.evaluation_config.get('objective_weights', [1.0, 1.0, 1.0])

        # 缓存机制
        self.cache_enabled = self.evaluation_config.get('cache_enabled', True)
        self.fitness_cache = {}

        logger.info("基于分类学习的适应度评估器初始化完成")
        logger.info(f"使用帕累托分类: {self.use_pareto_classification}")
        logger.info(f"目标函数权重: {self.objective_weights}")

    def evaluate_sequence_quality(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        评估温度序列质量（基于多目标函数）

        Args:
            temperature_sequence: 温度序列

        Returns:
            包含各种质量指标的字典
        """
        # 检查缓存
        if self.cache_enabled:
            cache_key = hash(temperature_sequence.tobytes())
            if cache_key in self.fitness_cache:
                return self.fitness_cache[cache_key]

        # 初始化多目标函数计算器（延迟初始化）
        if not hasattr(self, 'multi_obj_functions'):
            self.multi_obj_functions = MultiObjectiveFunctions(
                classifier=self.classifier,
                feature_extractor=self.feature_extractor,
                config_path="config/config.yaml"
            )

        # 计算三个目标函数
        objectives = self.multi_obj_functions.evaluate_all_objectives(temperature_sequence)
        normalized_objectives = self.multi_obj_functions.normalize_objectives(objectives)

        # 计算加权综合评分
        weighted_score = sum(w * obj for w, obj in zip(self.objective_weights, normalized_objectives))

        # 构建评估结果
        quality_metrics = {
            'f1_label1': objectives[0],
            'f2_label2': objectives[1],
            'f3_smoothness': objectives[2],
            'f1_normalized': normalized_objectives[0],
            'f2_normalized': normalized_objectives[1],
            'f3_normalized': normalized_objectives[2],
            'weighted_score': weighted_score,
            'overall_quality': weighted_score / sum(self.objective_weights)  # 标准化到[0,1]
        }

        # 缓存结果
        if self.cache_enabled:
            self.fitness_cache[cache_key] = quality_metrics

        return quality_metrics

    def compare_sequences(self, seq1: np.ndarray, seq2: np.ndarray) -> int:
        """
        比较两个序列的质量（基于分类学习）

        Args:
            seq1: 序列1
            seq2: 序列2

        Returns:
            1 如果seq1更好，0 如果seq2更好
        """
        # 评估两个序列
        quality1 = self.evaluate_sequence_quality(seq1)
        quality2 = self.evaluate_sequence_quality(seq2)

        # 基于综合评分比较
        score1 = quality1['overall_quality']
        score2 = quality2['overall_quality']

        return 1 if score1 > score2 else 0

    def evaluate_population_fitness(self, population: List[np.ndarray]) -> List[float]:
        """
        评估种群中所有个体的适应度

        Args:
            population: 温度序列种群

        Returns:
            适应度值列表
        """
        fitness_values = []

        for sequence in population:
            quality_metrics = self.evaluate_sequence_quality(sequence)
            fitness = quality_metrics['overall_quality']
            fitness_values.append(fitness)

        return fitness_values

    def get_pareto_classification(self, sequences: List[np.ndarray]) -> List[str]:
        """
        基于帕累托支配关系对序列进行分类

        Args:
            sequences: 温度序列列表

        Returns:
            分类标签列表 ('good' 或 'poor')
        """
        if not hasattr(self, 'pareto_analyzer'):
            self.pareto_analyzer = ParetoAnalyzer(config_path="config/config.yaml")

        # 计算所有序列的目标函数值
        objective_values = {}
        for i, seq in enumerate(sequences):
            if not hasattr(self, 'multi_obj_functions'):
                self.multi_obj_functions = MultiObjectiveFunctions(
                    classifier=self.classifier,
                    feature_extractor=self.feature_extractor,
                    config_path="config/config.yaml"
                )
            objectives = self.multi_obj_functions.evaluate_all_objectives(seq)
            objective_values[i] = objectives

        # 使用帕累托分析器进行分类
        self.pareto_analyzer.objective_values = objective_values
        classification = self.pareto_analyzer.classify_samples_by_pareto()

        # 转换为列表格式
        return [classification.get(i, 'poor') for i in range(len(sequences))]

    def get_multi_objective_function(self):
        """
        获取多目标函数评估器

        Returns:
            多目标函数评估器的 evaluate_all_objectives 方法
        """
        # 创建多目标函数计算器实例
        if not hasattr(self, 'multi_obj_functions'):
            self.multi_obj_functions = MultiObjectiveFunctions(
                classifier=self.classifier,
                feature_extractor=self.feature_extractor,
                config_path="config/config.yaml"
            )

        # 返回多目标函数评估方法
        return self.multi_obj_functions.evaluate_all_objectives


class FitnessEvaluator:
    """适应度评估器"""
    
    def __init__(self, classifier, feature_extractor, reference_sequences: List[np.ndarray],
                 config_path: str = "config/config.yaml"):
        """
        初始化适应度评估器
        
        Args:
            classifier: 训练好的序列分类器
            feature_extractor: 特征提取器
            reference_sequences: 参考序列列表（用于比较）
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        self.classifier = classifier
        self.feature_extractor = feature_extractor
        self.reference_sequences = reference_sequences

        # 评估策略配置
        self.evaluation_strategy = "ensemble"  # single, multiple, ensemble
        self.num_comparisons = min(5, len(reference_sequences))  # 每次比较的参考序列数量

        # 变长序列配置
        self.temp_config = self.config['pso']['temperature_sequence']
        self.variable_length_config = self.temp_config.get('variable_length', {})
        self.variable_length_enabled = self.variable_length_config.get('enable', False)

        if self.variable_length_enabled:
            self.min_length = self.variable_length_config['min_length']
            self.max_length = self.variable_length_config['max_length']
            self.default_length = self.variable_length_config['default_length']
            self.length_weight = self.variable_length_config['length_weight']
            self.length_penalty_factor = self.variable_length_config['length_penalty_factor']

            logger.info(f"适应度评估器启用变长序列支持")
            logger.info(f"长度范围: [{self.min_length}, {self.max_length}]")
            logger.info(f"长度权重: {self.length_weight}")

        # 缓存机制
        self.fitness_cache = {}
        self.cache_enabled = True

        logger.info(f"适应度评估器初始化完成，参考序列数量: {len(reference_sequences)}")
    
    def _sequence_to_key(self, sequence: np.ndarray) -> str:
        """
        将序列转换为缓存键
        
        Args:
            sequence: 温度序列
            
        Returns:
            缓存键字符串
        """
        # 使用序列的哈希值作为键
        return str(hash(sequence.tobytes()))
    
    def _calculate_length_penalty(self, sequence: np.ndarray) -> float:
        """
        计算序列长度惩罚

        Args:
            sequence: 温度序列

        Returns:
            长度惩罚分数 (0-1, 1表示无惩罚)
        """
        if not self.variable_length_enabled:
            return 1.0

        seq_length = len(sequence)

        # 计算长度偏离程度
        if seq_length < self.min_length or seq_length > self.max_length:
            # 超出范围的严重惩罚
            return 0.1

        # 计算相对于默认长度的偏离
        length_deviation = abs(seq_length - self.default_length) / self.default_length

        # 应用惩罚因子
        penalty = max(0.0, 1.0 - length_deviation * self.length_penalty_factor)

        return penalty

    def _normalize_sequences_for_comparison(self, seq1: np.ndarray,
                                          seq2: np.ndarray) -> tuple:
        """
        为比较标准化不同长度的序列

        Args:
            seq1: 第一个序列
            seq2: 第二个序列

        Returns:
            标准化后的序列对
        """
        if not self.variable_length_enabled or len(seq1) == len(seq2):
            return seq1, seq2

        # 选择较短的长度作为目标长度
        target_length = min(len(seq1), len(seq2))

        # 重采样到相同长度
        if len(seq1) != target_length:
            indices = np.linspace(0, len(seq1) - 1, target_length).astype(int)
            seq1_normalized = seq1[indices]
        else:
            seq1_normalized = seq1

        if len(seq2) != target_length:
            indices = np.linspace(0, len(seq2) - 1, target_length).astype(int)
            seq2_normalized = seq2[indices]
        else:
            seq2_normalized = seq2

        return seq1_normalized, seq2_normalized

    def evaluate_single_comparison(self, target_sequence: np.ndarray,
                                 reference_sequence: np.ndarray) -> float:
        """
        评估目标序列与单个参考序列的比较结果 (支持变长序列)

        Args:
            target_sequence: 目标序列
            reference_sequence: 参考序列

        Returns:
            比较得分 (0-1)
        """
        try:
            # 标准化序列长度以便比较
            target_norm, reference_norm = self._normalize_sequences_for_comparison(
                target_sequence, reference_sequence
            )

            # 检查分类器类型并调用相应方法
            if hasattr(self.classifier, 'predict_comparison'):
                # 如果是SequenceClassifier对象
                result = self.classifier.predict_comparison(
                    target_norm, reference_norm, self.feature_extractor
                )
                base_score = result['probability_seq1_better']
            else:
                # 如果是原始的SVC对象，手动构造特征并预测
                pair_data = [{
                    'sequence_1': target_norm,
                    'sequence_2': reference_norm,
                    'label': 0  # 占位符
                }]

                # 提取特征
                features = self.feature_extractor.transform(pair_data)

                # 预测概率
                if hasattr(self.classifier, 'predict_proba'):
                    probability = self.classifier.predict_proba(features)[0]
                    # 返回序列1更好的概率（类别1的概率）
                    base_score = float(probability[1]) if len(probability) > 1 else 0.5
                else:
                    # 如果没有概率预测，使用决策函数
                    decision = self.classifier.decision_function(features)[0]
                    # 将决策函数值转换为概率（sigmoid函数）
                    base_score = 1.0 / (1.0 + np.exp(-decision))

            # 应用长度惩罚
            if self.variable_length_enabled:
                length_penalty = self._calculate_length_penalty(target_sequence)
                final_score = base_score * (1 - self.length_weight) + length_penalty * self.length_weight
            else:
                final_score = base_score

            return float(final_score)

        except Exception as e:
            logger.warning(f"单次比较评估失败: {e}")
            return 0.5  # 返回中性分数
    
    def evaluate_multiple_comparisons(self, target_sequence: np.ndarray) -> float:
        """
        评估目标序列与多个参考序列的比较结果
        
        Args:
            target_sequence: 目标序列
            
        Returns:
            综合适应度分数
        """
        if len(self.reference_sequences) == 0:
            return 0.5
        
        # 随机选择参考序列进行比较
        selected_references = random.sample(
            self.reference_sequences, 
            min(self.num_comparisons, len(self.reference_sequences))
        )
        
        comparison_scores = []
        
        for ref_seq in selected_references:
            score = self.evaluate_single_comparison(target_sequence, ref_seq)
            comparison_scores.append(score)
        
        # 计算平均得分
        average_score = np.mean(comparison_scores)
        
        return average_score
    
    def evaluate_ensemble(self, target_sequence: np.ndarray) -> float:
        """
        集成评估策略：结合多种评估方法 (支持变长序列)

        Args:
            target_sequence: 目标序列

        Returns:
            集成适应度分数
        """
        scores = []
        weights = []

        # 1. 多重比较得分
        multi_score = self.evaluate_multiple_comparisons(target_sequence)
        scores.append(multi_score)
        weights.append(0.6)  # 比较得分权重最高

        # 2. 序列质量内在指标
        intrinsic_score = self._evaluate_intrinsic_quality(target_sequence)
        scores.append(intrinsic_score)
        weights.append(0.2)

        # 3. 稳定性评估
        stability_score = self._evaluate_stability(target_sequence)
        scores.append(stability_score)
        weights.append(0.2)

        # 4. 变长序列特有：长度适应性评估
        if self.variable_length_enabled:
            length_score = self._evaluate_length_fitness(target_sequence)
            scores.append(length_score)
            weights.append(self.length_weight)

            # 重新标准化权重
            total_weight = sum(weights)
            weights = [w / total_weight for w in weights]

        # 加权平均
        ensemble_score = np.average(scores, weights=weights)

        return ensemble_score

    def _evaluate_length_fitness(self, sequence: np.ndarray) -> float:
        """
        评估序列长度的适应性

        Args:
            sequence: 温度序列

        Returns:
            长度适应性分数 (0-1)
        """
        if not self.variable_length_enabled:
            return 1.0

        seq_length = len(sequence)

        # 1. 基本长度合规性
        if seq_length < self.min_length or seq_length > self.max_length:
            return 0.0

        # 2. 相对于默认长度的适应性
        length_ratio = seq_length / self.default_length

        # 3. 长度效率评估 (较短序列在质量相同时更优)
        efficiency_bonus = 1.0 / (1.0 + (seq_length - self.min_length) / (self.max_length - self.min_length))

        # 4. 综合长度分数
        length_fitness = 0.7 * (1.0 - abs(length_ratio - 1.0)) + 0.3 * efficiency_bonus

        return max(0.0, min(1.0, length_fitness))
    
    def _evaluate_intrinsic_quality(self, sequence: np.ndarray) -> float:
        """
        评估序列的内在质量指标
        
        Args:
            sequence: 温度序列
            
        Returns:
            内在质量分数 (0-1)
        """
        try:
            # 1. 温度范围合理性
            temp_range_score = self._evaluate_temperature_range(sequence)
            
            # 2. 变化平滑性
            smoothness_score = self._evaluate_smoothness(sequence)
            
            # 3. 趋势合理性
            trend_score = self._evaluate_trend(sequence)
            
            # 综合评分
            intrinsic_score = np.mean([temp_range_score, smoothness_score, trend_score])
            
            return intrinsic_score
            
        except Exception as e:
            logger.warning(f"内在质量评估失败: {e}")
            return 0.5
    
    def _evaluate_temperature_range(self, sequence: np.ndarray) -> float:
        """评估温度范围的合理性"""
        min_temp = self.config['pso']['temperature_sequence']['min_temperature']
        max_temp = self.config['pso']['temperature_sequence']['max_temperature']
        
        # 检查是否在合理范围内
        if np.all((sequence >= min_temp) & (sequence <= max_temp)):
            # 进一步评估温度分布
            temp_std = np.std(sequence)
            optimal_std = (max_temp - min_temp) * 0.1  # 期望的标准差
            
            # 标准差越接近期望值，得分越高
            std_score = 1.0 - min(abs(temp_std - optimal_std) / optimal_std, 1.0)
            return std_score
        else:
            return 0.0
    
    def _evaluate_smoothness(self, sequence: np.ndarray) -> float:
        """评估序列的平滑性"""
        # 计算一阶和二阶差分
        diff1 = np.diff(sequence)
        diff2 = np.diff(sequence, n=2)
        
        # 平滑性指标：差分的方差越小越平滑
        smoothness1 = 1.0 / (1.0 + np.var(diff1))
        smoothness2 = 1.0 / (1.0 + np.var(diff2))
        
        return (smoothness1 + smoothness2) / 2
    
    def _evaluate_trend(self, sequence: np.ndarray) -> float:
        """评估序列趋势的合理性"""
        # 简单的趋势评估：避免过于剧烈的变化
        max_change_rate = self.config['pso']['temperature_sequence']['max_change_rate']
        
        # 计算最大变化率
        changes = np.abs(np.diff(sequence))
        max_change = np.max(changes)
        
        # 变化率在合理范围内得分更高
        if max_change <= max_change_rate:
            return 1.0
        else:
            return max(0.0, 1.0 - (max_change - max_change_rate) / max_change_rate)
    
    def _evaluate_stability(self, sequence: np.ndarray) -> float:
        """评估序列的稳定性"""
        # 计算序列的变异系数
        cv = np.std(sequence) / np.mean(sequence) if np.mean(sequence) > 0 else 1.0
        
        # 变异系数适中的序列更稳定
        optimal_cv = 0.1  # 期望的变异系数
        stability_score = 1.0 - min(abs(cv - optimal_cv) / optimal_cv, 1.0)
        
        return stability_score
    
    def evaluate_fitness(self, sequence: np.ndarray) -> float:
        """
        评估序列的适应度
        
        Args:
            sequence: 温度序列
            
        Returns:
            适应度分数
        """
        # 检查缓存
        if self.cache_enabled:
            cache_key = self._sequence_to_key(sequence)
            if cache_key in self.fitness_cache:
                return self.fitness_cache[cache_key]
        
        # 根据策略选择评估方法
        if self.evaluation_strategy == "single":
            if len(self.reference_sequences) > 0:
                ref_seq = random.choice(self.reference_sequences)
                fitness = self.evaluate_single_comparison(sequence, ref_seq)
            else:
                fitness = self._evaluate_intrinsic_quality(sequence)
                
        elif self.evaluation_strategy == "multiple":
            fitness = self.evaluate_multiple_comparisons(sequence)
            
        elif self.evaluation_strategy == "ensemble":
            fitness = self.evaluate_ensemble(sequence)
            
        else:
            raise ValueError(f"未知的评估策略: {self.evaluation_strategy}")
        
        # 缓存结果
        if self.cache_enabled:
            self.fitness_cache[cache_key] = fitness
        
        return fitness
    
    def get_fitness_function(self) -> Callable[[np.ndarray], float]:
        """
        获取适应度函数（用于PSO优化器）
        
        Returns:
            适应度评估函数
        """
        return self.evaluate_fitness
    
    def clear_cache(self):
        """清空适应度缓存"""
        self.fitness_cache.clear()
        logger.info("适应度缓存已清空")
    
    def get_cache_stats(self) -> Dict:
        """
        获取缓存统计信息
        
        Returns:
            缓存统计字典
        """
        return {
            'cache_size': len(self.fitness_cache),
            'cache_enabled': self.cache_enabled
        }
    
    def set_evaluation_strategy(self, strategy: str):
        """
        设置评估策略
        
        Args:
            strategy: 评估策略 ("single", "multiple", "ensemble")
        """
        valid_strategies = ["single", "multiple", "ensemble"]
        if strategy not in valid_strategies:
            raise ValueError(f"无效的评估策略: {strategy}. 有效选项: {valid_strategies}")
        
        self.evaluation_strategy = strategy
        logger.info(f"评估策略已设置为: {strategy}")
    
    def benchmark_evaluation_speed(self, test_sequence: np.ndarray, num_tests: int = 100) -> Dict:
        """
        基准测试评估速度
        
        Args:
            test_sequence: 测试序列
            num_tests: 测试次数
            
        Returns:
            性能统计字典
        """
        import time
        
        times = []
        
        for _ in range(num_tests):
            start_time = time.time()
            self.evaluate_fitness(test_sequence)
            end_time = time.time()
            times.append(end_time - start_time)
        
        stats = {
            'mean_time': np.mean(times),
            'std_time': np.std(times),
            'min_time': np.min(times),
            'max_time': np.max(times),
            'total_time': np.sum(times)
        }
        
        logger.info(f"评估速度基准测试完成: 平均时间 {stats['mean_time']:.4f}s")
        
        return stats


def main():
    """测试适应度评估器"""
    # 创建测试数据
    test_sequence = np.random.uniform(20, 150, 1000)
    reference_sequences = [
        np.random.uniform(20, 150, 1000) for _ in range(5)
    ]
    
    # 注意：这里需要实际的分类器和特征提取器
    # evaluator = FitnessEvaluator(classifier, feature_extractor, reference_sequences)
    # fitness = evaluator.evaluate_fitness(test_sequence)
    # print(f"适应度分数: {fitness:.4f}")
    
    print("适应度评估器测试需要训练好的分类器和特征提取器")


class MultiObjectiveFunctions:
    """多目标函数计算器"""

    def __init__(self, classifier=None, feature_extractor=None,
                 config_path: str = "config/config.yaml"):
        """
        初始化多目标函数计算器

        Args:
            classifier: 训练好的分类器（可选）
            feature_extractor: 特征提取器（可选）
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        # 存储模型
        self.classifier = classifier
        self.feature_extractor = feature_extractor

        # 加载参考数据
        self._load_reference_data()

        # 目标函数配置
        self.objective_config = self.config.get('multi_objective', {})

        # 温度范围配置（基于数据分析更新）
        temp_config = self.config.get('pso', {}).get('temperature_sequence', {})
        self.min_temp = temp_config.get('min_temperature', 76.0)
        self.max_temp = temp_config.get('max_temperature', 154.0)

        # 缓存机制
        self.cache_enabled = True
        self.objective_cache = {}

        logger.info("多目标函数计算器初始化完成")

    def _load_reference_data(self):
        """加载参考数据（label_1和label_2）"""
        try:
            data_dir = self.config['data']['data_dir']

            # 加载 label_1 数据
            label1_file = os.path.join(data_dir, "label_1.xlsx")
            df1 = pd.read_excel(label1_file, header=None)
            self.label_1_values = df1.iloc[:, 0].values

            # 加载 label_2 数据
            label2_file = os.path.join(data_dir, "label_2.xlsx")
            df2 = pd.read_excel(label2_file, header=None)
            self.label_2_values = df2.iloc[:, 0].values

            # 计算参考统计量
            self.label_1_stats = {
                'min': self.label_1_values.min(),
                'max': self.label_1_values.max(),
                'mean': self.label_1_values.mean(),
                'std': self.label_1_values.std()
            }

            self.label_2_stats = {
                'min': self.label_2_values.min(),
                'max': self.label_2_values.max(),
                'mean': self.label_2_values.mean(),
                'std': self.label_2_values.std()
            }

            logger.info(f"成功加载参考数据: Label1({len(self.label_1_values)}), Label2({len(self.label_2_values)})")

        except Exception as e:
            logger.error(f"加载参考数据失败: {e}")
            # 使用默认值（基于之前的数据分析）
            self.label_1_values = np.array([0.26])
            self.label_2_values = np.array([97.65])
            self.label_1_stats = {'min': 0.05, 'max': 0.47, 'mean': 0.26, 'std': 0.1}
            self.label_2_stats = {'min': 86.4, 'max': 99.16, 'mean': 97.65, 'std': 3.0}

    def temperature_to_pressure_sequence(self, temperature_sequence: np.ndarray) -> np.ndarray:
        """
        将温度序列转换为压强序列

        注意：这里需要根据实际的物理/化学模型来实现
        目前使用简化的线性关系作为示例

        Args:
            temperature_sequence: 温度序列

        Returns:
            压强序列
        """
        # 简化的温度-压强关系（需要根据实际情况调整）
        # 假设压强与温度成正比关系，加上一些非线性项
        pressure_sequence = (
            0.1 * temperature_sequence +  # 线性项
            0.001 * temperature_sequence**2 +  # 二次项
            np.random.normal(0, 0.01, len(temperature_sequence))  # 噪声项
        )

        return pressure_sequence

    def objective_1_label1_minimization(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数1：最小化 label_1 指标

        Args:
            temperature_sequence: 温度序列

        Returns:
            label_1 预测值（越小越好）
        """
        try:
            # 使用基于统计特征的启发式方法预测label_1
            # 这是一个简化的方法，基于温度序列的统计特征

            # 计算温度序列的关键统计特征
            mean_temp = np.mean(temperature_sequence)
            std_temp = np.std(temperature_sequence)
            max_temp = np.max(temperature_sequence)
            min_temp = np.min(temperature_sequence)
            temp_range = max_temp - min_temp

            # 计算温度变化率
            temp_diff = np.diff(temperature_sequence)
            mean_change_rate = np.mean(np.abs(temp_diff))

            # 基于经验公式预测label_1（这是一个简化的启发式方法）
            # 假设label_1与温度的稳定性和适中的温度水平相关

            # 标准化特征
            normalized_mean = (mean_temp - self.min_temp) / (self.max_temp - self.min_temp)
            normalized_std = std_temp / (self.max_temp - self.min_temp)
            normalized_change_rate = mean_change_rate / (self.max_temp - self.min_temp)

            # 启发式预测公式（基于假设：适中温度、低变异性更好）
            predicted_label1 = (
                0.4 * abs(normalized_mean - 0.5) +  # 偏离中等温度的惩罚
                0.3 * normalized_std +              # 温度变异性惩罚
                0.3 * normalized_change_rate         # 变化率惩罚
            )

            # 将结果映射到合理范围（基于参考数据）
            if hasattr(self, 'label_1_stats'):
                min_label1 = self.label_1_stats['min']
                max_label1 = self.label_1_stats['max']
                predicted_label1 = min_label1 + predicted_label1 * (max_label1 - min_label1)
            else:
                # 使用默认范围
                predicted_label1 = 80 + predicted_label1 * 20  # 假设范围[80, 100]

            return float(predicted_label1)

        except Exception as e:
            logger.warning(f"目标函数1计算失败: {e}")
            # 返回默认的中等值
            if hasattr(self, 'label_1_stats'):
                return self.label_1_stats['mean']
            else:
                return 85.0  # 默认值

        except Exception as e:
            logger.warning(f"目标函数1计算失败: {e}")
            return self.label_1_stats['mean']  # 返回平均值作为默认值

    def objective_2_label2_maximization(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数2：最大化 label_2 指标

        Args:
            temperature_sequence: 温度序列

        Returns:
            label_2 预测值（越大越好）
        """
        try:
            # 使用基于统计特征的启发式方法预测label_2
            # 这是一个简化的方法，基于温度序列的统计特征

            # 计算温度序列的关键统计特征
            mean_temp = np.mean(temperature_sequence)
            max_temp = np.max(temperature_sequence)
            min_temp = np.min(temperature_sequence)
            final_temp = temperature_sequence[-1]
            temp_range = max_temp - min_temp

            # 计算温度上升趋势
            temp_trend = final_temp - temperature_sequence[0]

            # 基于经验公式预测label_2（这是一个简化的启发式方法）
            # 假设label_2与较高的温度和良好的温度上升趋势相关

            # 标准化特征
            normalized_mean = (mean_temp - self.min_temp) / (self.max_temp - self.min_temp)
            normalized_max = (max_temp - self.min_temp) / (self.max_temp - self.min_temp)
            normalized_trend = temp_trend / (self.max_temp - self.min_temp)

            # 启发式预测公式（基于假设：较高温度、正向趋势更好）
            predicted_label2 = (
                0.4 * normalized_mean +      # 平均温度贡献
                0.3 * normalized_max +       # 最高温度贡献
                0.3 * max(0, normalized_trend)  # 正向温度趋势贡献
            )

            # 将结果映射到合理范围（基于参考数据）
            if hasattr(self, 'label_2_stats'):
                min_label2 = self.label_2_stats['min']
                max_label2 = self.label_2_stats['max']
                predicted_label2 = min_label2 + predicted_label2 * (max_label2 - min_label2)
            else:
                # 使用默认范围
                predicted_label2 = 90 + predicted_label2 * 10  # 假设范围[90, 100]

            return float(predicted_label2)

        except Exception as e:
            logger.warning(f"目标函数2计算失败: {e}")
            # 返回默认的中等值
            if hasattr(self, 'label_2_stats'):
                return self.label_2_stats['mean']
            else:
                return 95.0  # 默认值

        except Exception as e:
            logger.warning(f"目标函数2计算失败: {e}")
            return self.label_2_stats['mean']  # 返回平均值作为默认值

    def objective_3_pressure_smoothness(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数3：最大化压强序列平滑性（基于傅里叶变换）

        Args:
            temperature_sequence: 温度序列

        Returns:
            平滑性指标（越大越好）
        """
        try:
            # 将温度序列转换为压强序列
            pressure_sequence = self.temperature_to_pressure_sequence(temperature_sequence)

            # 计算傅里叶变换
            fft_coeffs = fft.fft(pressure_sequence)
            fft_magnitude = np.abs(fft_coeffs)

            # 计算频率
            n = len(pressure_sequence)
            freqs = fft.fftfreq(n)

            # 计算平滑性指标
            # 方法1：低频能量占比（越高越平滑）
            low_freq_threshold = 0.1  # 低频阈值
            low_freq_mask = np.abs(freqs) <= low_freq_threshold
            low_freq_energy = np.sum(fft_magnitude[low_freq_mask]**2)
            total_energy = np.sum(fft_magnitude**2)

            smoothness_ratio = low_freq_energy / (total_energy + 1e-8)

            # 方法2：高频惩罚
            high_freq_mask = np.abs(freqs) > low_freq_threshold
            high_freq_penalty = np.sum(fft_magnitude[high_freq_mask]**2) / (total_energy + 1e-8)

            # 综合平滑性指标
            smoothness_score = smoothness_ratio - 0.5 * high_freq_penalty

            # 标准化到[0, 1]范围
            smoothness_score = np.clip(smoothness_score, 0, 1)

            return float(smoothness_score)

        except Exception as e:
            logger.warning(f"目标函数3计算失败: {e}")
            return 0.5  # 返回中等平滑性作为默认值

    def objective_4_distribution_matching(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数4：统计分布匹配度最大化

        评估生成的温度序列与真实数据统计分布的匹配程度

        Args:
            temperature_sequence: 温度序列

        Returns:
            分布匹配度 (0-1，越大越好)
        """
        try:
            # 真实数据的统计特性（基于数据分析结果）
            target_mean = 132.6
            target_std = 12.3
            target_min = 13.1
            target_max = 151.3

            # 计算当前序列的统计特性
            current_mean = np.mean(temperature_sequence)
            current_std = np.std(temperature_sequence)
            current_min = np.min(temperature_sequence)
            current_max = np.max(temperature_sequence)
            current_range = current_max - current_min
            target_range = target_max - target_min

            # 1. 均值匹配度 (权重: 0.3)
            mean_diff = abs(current_mean - target_mean)
            mean_score = max(0, 1 - mean_diff / 50.0)  # 50°C容忍度

            # 2. 标准差匹配度 (权重: 0.25)
            std_ratio = min(current_std, target_std) / max(current_std, target_std)
            std_score = std_ratio

            # 3. 范围匹配度 (权重: 0.25)
            range_ratio = min(current_range, target_range) / max(current_range, target_range)
            range_score = range_ratio

            # 4. 温度区域分布匹配度 (权重: 0.2)
            # 定义温度区域
            low_temp_boundary = 50.0
            medium_temp_boundary = 100.0

            # 真实数据的区域分布（基于分析结果估算）
            target_low_ratio = 0.15
            target_medium_ratio = 0.25
            target_high_ratio = 0.60

            # 计算当前序列的区域分布
            low_mask = temperature_sequence < low_temp_boundary
            medium_mask = (temperature_sequence >= low_temp_boundary) & (temperature_sequence < medium_temp_boundary)
            high_mask = temperature_sequence >= medium_temp_boundary

            current_low_ratio = np.sum(low_mask) / len(temperature_sequence)
            current_medium_ratio = np.sum(medium_mask) / len(temperature_sequence)
            current_high_ratio = np.sum(high_mask) / len(temperature_sequence)

            # 计算区域分布匹配度
            low_diff = abs(current_low_ratio - target_low_ratio)
            medium_diff = abs(current_medium_ratio - target_medium_ratio)
            high_diff = abs(current_high_ratio - target_high_ratio)

            distribution_score = max(0, 1 - (low_diff + medium_diff + high_diff) / 3.0)

            # 综合评分
            total_score = (
                0.30 * mean_score +
                0.25 * std_score +
                0.25 * range_score +
                0.20 * distribution_score
            )

            return np.clip(total_score, 0, 1)

        except Exception as e:
            logger.warning(f"目标函数4计算失败: {e}")
            return 0.3  # 返回较低的匹配度作为默认值

    def evaluate_all_objectives(self, temperature_sequence: np.ndarray) -> Tuple[float, float, float, float]:
        """
        计算所有四个目标函数

        Args:
            temperature_sequence: 温度序列

        Returns:
            (f1, f2, f3, f4) 四个目标函数值的元组
        """
        # 检查缓存
        if self.cache_enabled:
            cache_key = hash(temperature_sequence.tobytes())
            if cache_key in self.objective_cache:
                return self.objective_cache[cache_key]

        # 计算四个目标函数
        f1 = self.objective_1_label1_minimization(temperature_sequence)
        f2 = self.objective_2_label2_maximization(temperature_sequence)
        f3 = self.objective_3_pressure_smoothness(temperature_sequence)
        f4 = self.objective_4_distribution_matching(temperature_sequence)

        objectives = (f1, f2, f3, f4)

        # 缓存结果
        if self.cache_enabled:
            self.objective_cache[cache_key] = objectives

        return objectives

    def normalize_objectives(self, objectives: Tuple[float, float, float, float]) -> Tuple[float, float, float, float]:
        """
        标准化目标函数值到[0, 1]范围

        Args:
            objectives: (f1, f2, f3, f4) 原始目标函数值

        Returns:
            标准化后的目标函数值
        """
        f1, f2, f3, f4 = objectives

        # f1: 最小化 label_1 (0.05-0.47) -> 转换为最大化 (1-normalized_f1)
        f1_normalized = 1.0 - (f1 - self.label_1_stats['min']) / (self.label_1_stats['max'] - self.label_1_stats['min'])
        f1_normalized = np.clip(f1_normalized, 0, 1)

        # f2: 最大化 label_2 (86.4-99.16) -> 直接标准化
        f2_normalized = (f2 - self.label_2_stats['min']) / (self.label_2_stats['max'] - self.label_2_stats['min'])
        f2_normalized = np.clip(f2_normalized, 0, 1)

        # f3: 最大化平滑性 (0-1) -> 已经标准化
        f3_normalized = np.clip(f3, 0, 1)

        # f4: 最大化分布匹配度 (0-1) -> 已经标准化
        f4_normalized = np.clip(f4, 0, 1)

        return (f1_normalized, f2_normalized, f3_normalized, f4_normalized)

    def get_multi_objective_function(self) -> Callable[[np.ndarray], List[float]]:
        """
        获取多目标函数（用于MOEA/D优化器）

        Returns:
            多目标函数，输入温度序列，返回[f1, f2, f3]
        """
        def multi_objective_function(temperature_sequence: np.ndarray) -> List[float]:
            """
            多目标函数实现

            Args:
                temperature_sequence: 温度序列

            Returns:
                [f1, f2, f3, f4] 目标函数值列表
                - f1: label_1最小化（越小越好）
                - f2: label_2最大化（越大越好，但在MOEA/D中转为最小化）
                - f3: 平滑性最大化（越大越好，但在MOEA/D中转为最小化）
                - f4: 分布匹配度最大化（越大越好，但在MOEA/D中转为最小化）
            """
            try:
                # 计算原始目标函数值
                f1, f2, f3, f4 = self.evaluate_all_objectives(temperature_sequence)

                # 对于MOEA/D，所有目标都需要转换为最小化问题
                # f1: 已经是最小化，保持不变
                # f2: 最大化转为最小化，取负值
                # f3: 最大化转为最小化，取负值
                # f4: 最大化转为最小化，取负值

                f1_min = f1  # 最小化label_1
                f2_min = -f2  # 最大化label_2转为最小化
                f3_min = -f3  # 最大化平滑性转为最小化
                f4_min = -f4  # 最大化分布匹配度转为最小化

                return [f1_min, f2_min, f3_min, f4_min]

            except Exception as e:
                logger.error(f"多目标函数计算失败: {e}")
                # 返回默认的较差值
                return [0.5, -90.0, -0.3, -0.3]

        return multi_objective_function

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取多目标函数的统计信息

        Returns:
            统计信息字典
        """
        stats = {
            'label_1_stats': self.label_1_stats,
            'label_2_stats': self.label_2_stats,
            'objective_config': self.objective_config,
            'cache_size': len(self.objective_cache) if self.cache_enabled else 0
        }

        return stats


class ParetoAnalyzer:
    """帕累托支配关系分析器"""

    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化帕累托分析器

        Args:
            config_path: 配置文件路径
        """
        self.config_path = config_path

        # 初始化数据处理器和多目标函数计算器
        from data_processor import DataProcessor
        self.data_processor = DataProcessor(config_path)
        self.multi_obj_functions = MultiObjectiveFunctions(config_path=config_path)

        # 存储样本数据和目标函数值
        self.sample_data = {}
        self.objective_values = {}
        self.pareto_classification = {}

        logger.info("帕累托分析器初始化完成")

    def load_all_samples(self) -> Dict[int, np.ndarray]:
        """
        加载所有21个样本的温度序列

        Returns:
            样本ID到温度序列的映射
        """
        logger.info("开始加载所有样本数据...")

        # 加载温度序列
        temperature_sequences = self.data_processor.load_temperature_sequences()

        # 转换为整数ID映射
        sample_data = {}
        for sample_id_str, sequence in temperature_sequences.items():
            # 提取数字ID
            sample_id = int(sample_id_str)
            sample_data[sample_id] = sequence

        self.sample_data = sample_data
        logger.info(f"成功加载 {len(sample_data)} 个样本")

        return sample_data

    def calculate_all_objectives(self) -> Dict[int, Tuple[float, float, float]]:
        """
        计算所有样本的三个目标函数值

        Returns:
            样本ID到目标函数值(f1, f2, f3)的映射
        """
        logger.info("开始计算所有样本的目标函数值...")

        if not self.sample_data:
            self.load_all_samples()

        objective_values = {}

        for sample_id, sequence in self.sample_data.items():
            logger.info(f"计算样本 {sample_id} 的目标函数...")

            # 计算三个目标函数
            objectives = self.multi_obj_functions.evaluate_all_objectives(sequence)
            objective_values[sample_id] = objectives

            logger.info(f"样本 {sample_id}: f1={objectives[0]:.4f}, f2={objectives[1]:.4f}, f3={objectives[2]:.4f}")

        self.objective_values = objective_values
        logger.info("所有样本的目标函数计算完成")

        return objective_values

    def is_dominated(self, obj1: Tuple[float, float, float],
                    obj2: Tuple[float, float, float]) -> bool:
        """
        判断obj1是否被obj2支配

        注意：
        - f1 (label_1): 越小越好
        - f2 (label_2): 越大越好
        - f3 (平滑性): 越大越好

        Args:
            obj1: 目标函数值1 (f1, f2, f3)
            obj2: 目标函数值2 (f1, f2, f3)

        Returns:
            True如果obj1被obj2支配
        """
        f1_1, f2_1, f3_1 = obj1
        f1_2, f2_2, f3_2 = obj2

        # obj2支配obj1的条件：
        # 1. f1_2 <= f1_1 (label_1越小越好)
        # 2. f2_2 >= f2_1 (label_2越大越好)
        # 3. f3_2 >= f3_1 (平滑性越大越好)
        # 4. 至少有一个严格不等式

        better_or_equal = (f1_2 <= f1_1) and (f2_2 >= f2_1) and (f3_2 >= f3_1)
        strictly_better = (f1_2 < f1_1) or (f2_2 > f2_1) or (f3_2 > f3_1)

        return better_or_equal and strictly_better

    def classify_samples_by_pareto(self) -> Dict[int, str]:
        """
        基于帕累托支配关系将样本分为好/差两类

        Returns:
            样本ID到分类标签('good'/'poor')的映射
        """
        logger.info("开始基于帕累托支配关系分类样本...")

        if not self.objective_values:
            self.calculate_all_objectives()

        sample_ids = list(self.objective_values.keys())
        domination_count = {}  # 每个样本被支配的次数

        # 计算每个样本被支配的次数
        for sample_id in sample_ids:
            domination_count[sample_id] = 0
            obj1 = self.objective_values[sample_id]

            for other_id in sample_ids:
                if sample_id != other_id:
                    obj2 = self.objective_values[other_id]
                    if self.is_dominated(obj1, obj2):
                        domination_count[sample_id] += 1

        # 基于被支配次数分类
        # 策略：被支配次数少的为好样本，多的为差样本
        domination_counts = list(domination_count.values())
        threshold = np.median(domination_counts)  # 使用中位数作为阈值

        classification = {}
        good_samples = []
        poor_samples = []

        for sample_id, count in domination_count.items():
            if count <= threshold:
                classification[sample_id] = 'good'
                good_samples.append(sample_id)
            else:
                classification[sample_id] = 'poor'
                poor_samples.append(sample_id)

        self.pareto_classification = classification

        logger.info(f"帕累托分类完成:")
        logger.info(f"  好样本 ({len(good_samples)}): {sorted(good_samples)}")
        logger.info(f"  差样本 ({len(poor_samples)}): {sorted(poor_samples)}")
        logger.info(f"  分类阈值 (被支配次数): {threshold}")

        return classification

    def generate_binary_training_data(self) -> List[Dict]:
        """
        生成二分类器训练数据

        Returns:
            成对比较数据列表，标签为0(差)或1(好)
        """
        logger.info("生成二分类器训练数据...")

        if not self.pareto_classification:
            self.classify_samples_by_pareto()

        training_data = []

        # 生成所有可能的样本对
        sample_ids = list(self.sample_data.keys())
        for i, id1 in enumerate(sample_ids):
            for j, id2 in enumerate(sample_ids):
                if i < j:  # 避免重复对
                    # 获取分类标签
                    label1 = self.pareto_classification[id1]
                    label2 = self.pareto_classification[id2]

                    # 确定比较标签
                    if label1 == 'good' and label2 == 'poor':
                        comparison_label = 1  # 序列1更好
                    elif label1 == 'poor' and label2 == 'good':
                        comparison_label = 0  # 序列2更好
                    else:
                        # 同类样本，基于目标函数值细分
                        obj1 = self.objective_values[id1]
                        obj2 = self.objective_values[id2]

                        # 使用简单的加权评分比较
                        score1 = -obj1[0] + obj1[1] + obj1[2]  # f1越小越好，f2和f3越大越好
                        score2 = -obj2[0] + obj2[1] + obj2[2]

                        comparison_label = 1 if score1 > score2 else 0

                    # 创建训练样本
                    training_sample = {
                        'sample_id_1': id1,
                        'sample_id_2': id2,
                        'sequence_1': self.sample_data[id1],
                        'sequence_2': self.sample_data[id2],
                        'label': comparison_label,
                        'pareto_class_1': label1,
                        'pareto_class_2': label2,
                        'objectives_1': self.objective_values[id1],
                        'objectives_2': self.objective_values[id2]
                    }

                    training_data.append(training_sample)

        logger.info(f"生成了 {len(training_data)} 个训练样本对")

        return training_data


class ParetoSolutionSelector:
    """Pareto解集中最优解选择器"""

    def __init__(self):
        """初始化解选择器"""
        self.logger = logging.getLogger(__name__)

    def calculate_membership_function_values(self, objective_matrix):
        """
        计算归一化成员函数值并选择最优解

        Args:
            objective_matrix: N×M矩阵，N个个体在M个目标上的原始目标值
                            F[k][i] 表示第k个个体在第i个目标上的值

        Returns:
            dict: 包含以下内容的字典
                - F_min: 长度为M的列表，每个目标的最小值
                - F_max: 长度为M的列表，每个目标的最大值
                - mu_matrix: N×M的归一化成员函数矩阵
                - mu_k: 长度为N的综合成员函数值列表
                - sorted_indices: 按μ_k从大到小排序的个体索引
                - best_solution_index: 性能最好的解的索引
        """
        import numpy as np

        objective_matrix = np.array(objective_matrix)
        N, M = objective_matrix.shape  # N个个体，M个目标

        self.logger.info(f"计算{N}个个体在{M}个目标上的归一化成员函数值")

        # 步骤1: 计算每个目标的最小值和最大值
        F_min = np.min(objective_matrix, axis=0).tolist()
        F_max = np.max(objective_matrix, axis=0).tolist()

        self.logger.info(f"目标函数范围:")
        for i in range(M):
            self.logger.info(f"  目标{i+1}: [{F_min[i]:.6f}, {F_max[i]:.6f}]")

        # 步骤2: 计算归一化成员函数矩阵
        mu_matrix = np.zeros((N, M))

        for k in range(N):
            for i in range(M):
                F_ki = objective_matrix[k, i]
                F_min_i = F_min[i]
                F_max_i = F_max[i]

                if F_ki <= F_min_i:
                    mu_matrix[k, i] = 1.0
                elif F_min_i < F_ki < F_max_i:
                    mu_matrix[k, i] = (F_max_i - F_ki) / (F_max_i - F_min_i)
                else:  # F_ki >= F_max_i
                    mu_matrix[k, i] = 0.0

        # 步骤3: 计算每个个体的综合成员函数值
        # μ_k = (1 / (N × M)) × ∑_{i=1..M} ∑_{j=1..N} μ[j][i]
        total_sum = np.sum(mu_matrix)  # 所有μ[j][i]的总和
        mu_k = np.full(N, total_sum / (N * M))  # 每个个体的综合成员函数值相同

        # 注意：根据公式，所有个体的μ_k值相同，这可能是公式的问题
        # 更合理的做法是计算每个个体自己的成员函数值平均：
        mu_k_individual = np.mean(mu_matrix, axis=1)  # 每个个体在所有目标上的平均成员函数值

        # 步骤4: 按μ_k从大到小排序个体索引
        sorted_indices = np.argsort(mu_k_individual)[::-1].tolist()  # 降序排列
        best_solution_index = sorted_indices[0]

        self.logger.info(f"成员函数值计算完成:")
        self.logger.info(f"  最优解索引: {best_solution_index}")
        self.logger.info(f"  最优解成员函数值: {mu_k_individual[best_solution_index]:.6f}")
        self.logger.info(f"  最优解目标函数值: {objective_matrix[best_solution_index]}")

        # 输出详细信息
        self.logger.info(f"\n=== 归一化成员函数值详细结果 ===")
        self.logger.info(f"F_min: {F_min}")
        self.logger.info(f"F_max: {F_max}")
        self.logger.info(f"μ矩阵 (前5个个体):")
        for k in range(min(5, N)):
            self.logger.info(f"  个体{k}: {mu_matrix[k]}")
        self.logger.info(f"μ_k值 (前5个个体): {mu_k_individual[:5]}")
        self.logger.info(f"排序后的个体索引 (前5个): {sorted_indices[:5]}")

        return {
            'F_min': F_min,
            'F_max': F_max,
            'mu_matrix': mu_matrix.tolist(),
            'mu_k': mu_k_individual.tolist(),
            'sorted_indices': sorted_indices,
            'best_solution_index': best_solution_index,
            'best_solution_objectives': objective_matrix[best_solution_index].tolist(),
            'best_solution_membership': mu_k_individual[best_solution_index]
        }

    def select_best_solution_from_pareto_front(self, pareto_solutions, pareto_objectives):
        """
        从Pareto前沿中选择性能最好的解

        Args:
            pareto_solutions: Pareto前沿解集（温度序列列表）
            pareto_objectives: Pareto前沿目标函数值矩阵

        Returns:
            dict: 包含最优解信息的字典
        """
        if len(pareto_solutions) == 0:
            raise ValueError("Pareto解集为空")

        # 计算归一化成员函数值
        membership_results = self.calculate_membership_function_values(pareto_objectives)

        best_index = membership_results['best_solution_index']
        best_solution = pareto_solutions[best_index]

        result = {
            'best_solution': best_solution,
            'best_solution_index': best_index,
            'best_objectives': membership_results['best_solution_objectives'],
            'membership_value': membership_results['best_solution_membership'],
            'membership_analysis': membership_results,
            'pareto_front_size': len(pareto_solutions)
        }

        self.logger.info(f"从{len(pareto_solutions)}个Pareto解中选择了第{best_index}个解作为最优解")

        return result


class ClassificationBasedPredictor:
    """基于分类学习的label_1和label_2预测器"""

    def __init__(self, feature_extractor=None, config_path: str = "config/config.yaml"):
        """
        初始化基于分类的预测器

        Args:
            feature_extractor: 特征提取器
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)

        self.feature_extractor = feature_extractor

        # 预测器配置
        self.predictor_config = self.config.get('classification_predictor', {})
        self.label1_bins = self.predictor_config.get('label1_bins', 5)  # label_1分箱数量
        self.label2_bins = self.predictor_config.get('label2_bins', 5)  # label_2分箱数量

        # 分类器
        self.label1_classifier = None
        self.label2_classifier = None

        # 标签分箱器
        self.label1_binning = None
        self.label2_binning = None

        # 训练数据
        self.training_data = None

        logger.info("基于分类学习的预测器初始化完成")
        logger.info(f"Label1分箱数: {self.label1_bins}, Label2分箱数: {self.label2_bins}")

    def prepare_training_data(self) -> Dict[str, Any]:
        """
        准备训练数据：加载21个样本的温度序列和对应的label值

        Returns:
            包含特征和标签的训练数据字典
        """
        logger.info("准备分类预测器训练数据...")

        # 加载数据
        from data_processor import DataProcessor
        data_processor = DataProcessor(config_path="config/config.yaml")

        # 加载温度序列
        temperature_sequences = data_processor.load_temperature_sequences()

        # 加载label数据
        multi_obj_functions = MultiObjectiveFunctions(config_path="config/config.yaml")
        label_1_values = multi_obj_functions.label_1_values
        label_2_values = multi_obj_functions.label_2_values

        # 提取特征
        if self.feature_extractor is None:
            from feature_extractor import FeatureExtractor
            self.feature_extractor = FeatureExtractor(config_path="config/config.yaml")

        features_list = []
        labels_1 = []
        labels_2 = []
        sample_ids = []

        for i, (sample_id_str, sequence) in enumerate(temperature_sequences.items()):
            # 提取特征
            features = self.feature_extractor.extract_sequence_features(sequence)
            features_list.append(features)

            # 获取对应的label值
            sample_idx = int(sample_id_str) - 1  # 转换为0-based索引
            if sample_idx < len(label_1_values):
                labels_1.append(label_1_values[sample_idx])
                labels_2.append(label_2_values[sample_idx])
                sample_ids.append(sample_id_str)
            else:
                logger.warning(f"样本 {sample_id_str} 没有对应的label数据")

        # 转换为numpy数组
        features_matrix = np.array(features_list)
        labels_1 = np.array(labels_1)
        labels_2 = np.array(labels_2)

        # 标准化特征
        if not hasattr(self.feature_extractor, 'is_fitted') or not self.feature_extractor.is_fitted:
            features_matrix, _ = self.feature_extractor.fit_transform([seq for seq in temperature_sequences.values()])
        else:
            features_matrix = self.feature_extractor.transform([seq for seq in temperature_sequences.values()])

        self.training_data = {
            'features': features_matrix,
            'labels_1': labels_1,
            'labels_2': labels_2,
            'sample_ids': sample_ids
        }

        logger.info(f"训练数据准备完成: {len(sample_ids)}个样本, {features_matrix.shape[1]}维特征")

        return self.training_data

    def create_label_bins(self, labels: np.ndarray, n_bins: int) -> np.ndarray:
        """
        将连续标签值分箱为分类标签

        Args:
            labels: 连续标签值
            n_bins: 分箱数量

        Returns:
            分箱后的分类标签
        """
        from sklearn.preprocessing import KBinsDiscretizer

        # 创建分箱器
        binning = KBinsDiscretizer(n_bins=n_bins, encode='ordinal', strategy='quantile')

        # 分箱
        labels_binned = binning.fit_transform(labels.reshape(-1, 1)).flatten().astype(int)

        return labels_binned, binning

    def train_classifiers(self) -> Dict[str, Any]:
        """
        训练label_1和label_2的分类器

        Returns:
            训练结果字典
        """
        logger.info("开始训练分类预测器...")

        if self.training_data is None:
            self.prepare_training_data()

        from sklearn.ensemble import RandomForestClassifier
        from sklearn.model_selection import cross_val_score
        from sklearn.metrics import classification_report

        features = self.training_data['features']
        labels_1 = self.training_data['labels_1']
        labels_2 = self.training_data['labels_2']

        # 创建label_1分箱
        labels_1_binned, self.label1_binning = self.create_label_bins(labels_1, self.label1_bins)

        # 创建label_2分箱
        labels_2_binned, self.label2_binning = self.create_label_bins(labels_2, self.label2_bins)

        # 训练label_1分类器
        self.label1_classifier = RandomForestClassifier(
            n_estimators=100,
            random_state=42,
            max_depth=10
        )
        self.label1_classifier.fit(features, labels_1_binned)

        # 训练label_2分类器
        self.label2_classifier = RandomForestClassifier(
            n_estimators=100,
            random_state=42,
            max_depth=10
        )
        self.label2_classifier.fit(features, labels_2_binned)

        # 交叉验证评估
        cv_scores_1 = cross_val_score(self.label1_classifier, features, labels_1_binned, cv=5)
        cv_scores_2 = cross_val_score(self.label2_classifier, features, labels_2_binned, cv=5)

        training_results = {
            'label1_cv_score': cv_scores_1.mean(),
            'label1_cv_std': cv_scores_1.std(),
            'label2_cv_score': cv_scores_2.mean(),
            'label2_cv_std': cv_scores_2.std(),
            'label1_bins': self.label1_bins,
            'label2_bins': self.label2_bins,
            'n_samples': len(features),
            'n_features': features.shape[1]
        }

        logger.info(f"分类器训练完成:")
        logger.info(f"  Label1分类器 CV准确率: {cv_scores_1.mean():.3f} ± {cv_scores_1.std():.3f}")
        logger.info(f"  Label2分类器 CV准确率: {cv_scores_2.mean():.3f} ± {cv_scores_2.std():.3f}")

        return training_results

    def predict_labels(self, temperature_sequence: np.ndarray) -> Tuple[float, float]:
        """
        预测给定温度序列的label_1和label_2值

        Args:
            temperature_sequence: 温度序列

        Returns:
            (predicted_label1, predicted_label2)
        """
        if self.label1_classifier is None or self.label2_classifier is None:
            raise ValueError("分类器尚未训练，请先调用train_classifiers()")

        # 提取特征
        features = self.feature_extractor.extract_sequence_features(temperature_sequence)
        features = features.reshape(1, -1)  # 转换为2D数组

        # 预测分箱类别
        label1_bin = self.label1_classifier.predict(features)[0]
        label2_bin = self.label2_classifier.predict(features)[0]

        # 将分箱结果转换回连续值（使用分箱中心值）
        label1_bin_edges = self.label1_binning.bin_edges_[0]
        label2_bin_edges = self.label2_binning.bin_edges_[0]

        # 计算分箱中心值
        predicted_label1 = (label1_bin_edges[label1_bin] + label1_bin_edges[label1_bin + 1]) / 2
        predicted_label2 = (label2_bin_edges[label2_bin] + label2_bin_edges[label2_bin + 1]) / 2

        return predicted_label1, predicted_label2

    def get_prediction_probabilities(self, temperature_sequence: np.ndarray) -> Dict[str, np.ndarray]:
        """
        获取预测的概率分布

        Args:
            temperature_sequence: 温度序列

        Returns:
            包含两个标签预测概率的字典
        """
        if self.label1_classifier is None or self.label2_classifier is None:
            raise ValueError("分类器尚未训练，请先调用train_classifiers()")

        # 提取特征
        features = self.feature_extractor.extract_sequence_features(temperature_sequence)
        features = features.reshape(1, -1)

        # 获取预测概率
        label1_probs = self.label1_classifier.predict_proba(features)[0]
        label2_probs = self.label2_classifier.predict_proba(features)[0]

        return {
            'label1_probabilities': label1_probs,
            'label2_probabilities': label2_probs
        }


if __name__ == "__main__":
    main()
