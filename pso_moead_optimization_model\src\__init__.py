#!/usr/bin/env python3
"""
MOEA/D多目标温度序列优化系统

基于分解的多目标进化算法(MOEA/D)的温度序列优化系统。
同时优化统计偏差、模式匹配和阶段违反三个目标函数。

主要模块：
- moead_optimizer: MOEA/D多目标优化算法实现
- multi_objective_function: 多目标函数评估器
- business_data_analyzer: 业务数据分析器
- temperature_constraints: 温度约束处理器
- sequence_classifier: 序列分类器
- feature_extractor: 特征提取器
- utils: 工具函数

作者: AI Assistant
版本: 4.0.0 (MOEA/D专用版本)
"""

__version__ = "4.0.0"
__author__ = "AI Assistant"
__email__ = ""
__description__ = "MOEA/D多目标温度序列优化系统"

# 导入主要类和函数
from .moead_optimizer import MOEADOptimizer
from .moead_fitness_evaluator import MultiObjectiveFunctions
from .business_data_analyzer import BusinessDataAnalyzer
from .temperature_constraints import TemperatureConstraints
from .sequence_classifier import SequenceClassifier
from .feature_extractor import FeatureExtractor
from .utils import load_config, setup_logging, create_directories

__all__ = [
    "MOEADOptimizer",
    "MultiObjectiveFunctions",
    "BusinessDataAnalyzer",
    "TemperatureConstraints",
    "SequenceClassifier",
    "FeatureExtractor",
    "load_config",
    "setup_logging",
    "create_directories"
]
