{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 化工车间温度序列PSO优化系统 - 基础使用教程\n", "\n", "本教程将指导您完成系统的基本使用流程，包括：\n", "1. 环境设置和数据准备\n", "2. 分类器训练\n", "3. PSO优化执行\n", "4. 结果分析和可视化"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 环境设置"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import sys\n", "import os\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "\n", "# 添加src目录到路径\n", "sys.path.append('../src')\n", "\n", "# 导入项目模块\n", "from data_processor import DataProcessor\n", "from feature_extractor import FeatureExtractor\n", "from sequence_classifier import SequenceClassifier\n", "from pso_optimizer import PSOOptimizer\n", "from fitness_evaluator import FitnessEvaluator\n", "from sequence_generator import SequenceGenerator\n", "from performance_analyzer import PerformanceAnalyzer\n", "from utils import load_config, setup_logging\n", "\n", "print(\"✓ 环境设置完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 数据加载和预处理"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载配置\n", "config = load_config('../config/config.yaml')\n", "\n", "# 初始化数据处理器\n", "data_processor = DataProcessor('../config/config.yaml')\n", "\n", "# 加载温度序列数据\n", "print(\"正在加载温度序列数据...\")\n", "temperature_sequences = data_processor.load_temperature_sequences()\n", "print(f\"✓ 成功加载 {len(temperature_sequences)} 个温度序列\")\n", "\n", "# 加载质量标签\n", "print(\"正在加载质量标签...\")\n", "quality_labels = data_processor.load_quality_labels()\n", "print(f\"✓ 成功加载质量标签\")\n", "\n", "# 显示数据统计信息\n", "stats = data_processor.get_dataset_statistics()\n", "print(\"\\n数据集统计信息:\")\n", "for key, value in stats.items():\n", "    print(f\"  {key}: {value}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 可视化原始数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 可视化几个样本序列\n", "plt.figure(figsize=(15, 10))\n", "\n", "# 选择前4个样本进行可视化\n", "sample_ids = list(temperature_sequences.keys())[:4]\n", "\n", "for i, sample_id in enumerate(sample_ids):\n", "    plt.subplot(2, 2, i+1)\n", "    sequence = temperature_sequences[sample_id]\n", "    \n", "    # 为了可视化，对长序列进行下采样\n", "    if len(sequence) > 2000:\n", "        indices = np.linspace(0, len(sequence)-1, 2000, dtype=int)\n", "        sequence = sequence[indices]\n", "    \n", "    plt.plot(sequence, linewidth=1)\n", "    plt.title(f'样本 {sample_id} (长度: {len(temperature_sequences[sample_id])})')\n", "    plt.xlabel('时间步')\n", "    plt.ylabel('温度 (°C)')\n", "    plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✓ 原始数据可视化完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 构建成对比较数据集"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 创建成对比较数据集\n", "print(\"正在构建成对比较数据集...\")\n", "pairwise_data = data_processor.create_pairwise_dataset()\n", "print(f\"✓ 成功创建 {len(pairwise_data)} 个成对比较样本\")\n", "\n", "# 显示标签分布\n", "labels = [pair['label'] for pair in pairwise_data]\n", "label_counts = np.bincount(labels)\n", "print(f\"标签分布: 类别0={label_counts[0]}, 类别1={label_counts[1]}\")\n", "\n", "# 可视化质量评分分布\n", "quality_scores = [data_processor.calculate_quality_score(i) for i in range(1, 22)]\n", "\n", "plt.figure(figsize=(12, 4))\n", "\n", "plt.subplot(1, 2, 1)\n", "plt.bar(['类别0', '类别1'], label_counts)\n", "plt.title('成对比较标签分布')\n", "plt.ylabel('数量')\n", "\n", "plt.subplot(1, 2, 2)\n", "plt.hist(quality_scores, bins=10, alpha=0.7, edgecolor='black')\n", "plt.title('质量评分分布')\n", "plt.xlabel('综合质量评分')\n", "plt.ylabel('频次')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✓ 成对比较数据集构建完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 特征提取"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化特征提取器\n", "print(\"正在初始化特征提取器...\")\n", "feature_extractor = FeatureExtractor('../config/config.yaml')\n", "\n", "# 提取特征\n", "print(\"正在提取特征...\")\n", "X, feature_names = feature_extractor.fit_transform(pairwise_data)\n", "y = np.array([pair['label'] for pair in pairwise_data])\n", "\n", "print(f\"✓ 特征提取完成\")\n", "print(f\"  特征矩阵形状: {X.shape}\")\n", "print(f\"  特征数量: {len(feature_names)}\")\n", "print(f\"  样本数量: {len(y)}\")\n", "\n", "# 可视化特征分布\n", "plt.figure(figsize=(15, 5))\n", "\n", "plt.subplot(1, 3, 1)\n", "plt.hist(<PERSON><PERSON>flatten(), bins=50, alpha=0.7)\n", "plt.title('特征值分布')\n", "plt.xlabel('特征值')\n", "plt.ylabel('频次')\n", "\n", "plt.subplot(1, 3, 2)\n", "feature_means = np.mean(X, axis=0)\n", "plt.plot(feature_means)\n", "plt.title('特征均值')\n", "plt.xlabel('特征索引')\n", "plt.ylabel('均值')\n", "\n", "plt.subplot(1, 3, 3)\n", "feature_stds = np.std(X, axis=0)\n", "plt.plot(feature_stds)\n", "plt.title('特征标准差')\n", "plt.xlabel('特征索引')\n", "plt.ylabel('标准差')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✓ 特征分析完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 训练分类器"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化分类器\n", "print(\"正在初始化分类器...\")\n", "classifier = SequenceClassifier('../config/config.yaml')\n", "\n", "# 训练分类器\n", "print(\"正在训练分类器...\")\n", "training_results = classifier.train(X, y, feature_names)\n", "\n", "print(\"\\n✓ 分类器训练完成\")\n", "print(f\"  训练准确率: {training_results['train_accuracy']:.4f}\")\n", "print(f\"  测试准确率: {training_results['test_accuracy']:.4f}\")\n", "print(f\"  交叉验证准确率: {training_results['cv_accuracy_mean']:.4f} ± {training_results['cv_accuracy_std']:.4f}\")\n", "print(f\"  测试AUC: {training_results['test_auc']:.4f}\")\n", "\n", "# 可视化训练结果\n", "classifier.plot_training_results()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 设置PSO优化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 准备参考序列\n", "print(\"正在准备参考序列...\")\n", "reference_sequences = list(temperature_sequences.values())[:10]  # 使用前10个作为参考\n", "\n", "# 初始化适应度评估器\n", "print(\"正在初始化适应度评估器...\")\n", "fitness_evaluator = FitnessEvaluator(\n", "    classifier=classifier,\n", "    feature_extractor=feature_extractor,\n", "    reference_sequences=reference_sequences,\n", "    config_path='../config/config.yaml'\n", ")\n", "\n", "# 设置评估策略\n", "fitness_evaluator.set_evaluation_strategy(\"ensemble\")\n", "\n", "# 初始化PSO优化器\n", "print(\"正在初始化PSO优化器...\")\n", "pso_optimizer = PSOOptimizer('../config/config.yaml')\n", "\n", "print(f\"✓ PSO优化器设置完成\")\n", "print(f\"  粒子群大小: {pso_optimizer.swarm_size}\")\n", "print(f\"  最大迭代次数: {pso_optimizer.max_iterations}\")\n", "print(f\"  控制点数量: {pso_optimizer.control_points}\")\n", "print(f\"  温度范围: [{pso_optimizer.min_temp}, {pso_optimizer.max_temp}]°C\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 执行PSO优化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取适应度函数\n", "fitness_function = fitness_evaluator.get_fitness_function()\n", "\n", "# 执行优化（使用较少的迭代次数用于演示）\n", "print(\"正在执行PSO优化...\")\n", "print(\"注意：为了演示目的，使用较少的迭代次数\")\n", "\n", "# 临时修改迭代次数\n", "original_max_iter = pso_optimizer.max_iterations\n", "pso_optimizer.max_iterations = 20  # 减少迭代次数用于演示\n", "\n", "optimization_results = pso_optimizer.optimize(fitness_function)\n", "\n", "# 恢复原始设置\n", "pso_optimizer.max_iterations = original_max_iter\n", "\n", "print(\"\\n✓ PSO优化完成\")\n", "print(f\"  最佳适应度: {optimization_results['best_fitness']:.6f}\")\n", "print(f\"  总迭代次数: {optimization_results['total_iterations']}\")\n", "print(f\"  是否收敛: {optimization_results['converged']}\")\n", "print(f\"  最终多样性: {optimization_results['final_diversity']:.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 结果分析和可视化"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 获取最优温度序列\n", "best_sequence = np.array(optimization_results['best_temperature_sequence'])\n", "\n", "# 可视化优化过程\n", "plt.figure(figsize=(15, 10))\n", "\n", "# 1. 适应度收敛曲线\n", "plt.subplot(2, 3, 1)\n", "plt.plot(optimization_results['best_fitness_history'], 'b-', linewidth=2)\n", "plt.title('适应度收敛曲线')\n", "plt.xlabel('迭代次数')\n", "plt.ylabel('适应度')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 2. 粒子群多样性\n", "plt.subplot(2, 3, 2)\n", "plt.plot(optimization_results['diversity_history'], 'g-', linewidth=2)\n", "plt.title('粒子群多样性')\n", "plt.xlabel('迭代次数')\n", "plt.ylabel('多样性')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 3. 最优控制点\n", "plt.subplot(2, 3, 3)\n", "best_control_points = optimization_results['best_control_points']\n", "plt.plot(best_control_points, 'ro-', linewidth=2, markersize=8)\n", "plt.title('最优控制点')\n", "plt.xlabel('控制点索引')\n", "plt.ylabel('温度 (°C)')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 4. 最优温度序列\n", "plt.subplot(2, 3, 4)\n", "plt.plot(best_sequence, 'r-', linewidth=2)\n", "plt.title('最优温度序列')\n", "plt.xlabel('时间步')\n", "plt.ylabel('温度 (°C)')\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 5. 温度变化率\n", "plt.subplot(2, 3, 5)\n", "change_rates = np.abs(np.diff(best_sequence))\n", "plt.plot(change_rates, 'orange', linewidth=1)\n", "plt.axhline(y=pso_optimizer.max_change_rate, color='red', linestyle='--', \n", "           label=f'最大变化率: {pso_optimizer.max_change_rate}')\n", "plt.title('温度变化率')\n", "plt.xlabel('时间步')\n", "plt.ylabel('变化率 (°C/step)')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 6. 与参考序列对比\n", "plt.subplot(2, 3, 6)\n", "# 随机选择一个参考序列进行对比\n", "ref_sequence = reference_sequences[0]\n", "if len(ref_sequence) > len(best_sequence):\n", "    indices = np.linspace(0, len(ref_sequence)-1, len(best_sequence), dtype=int)\n", "    ref_sequence = ref_sequence[indices]\n", "elif len(ref_sequence) < len(best_sequence):\n", "    indices = np.linspace(0, len(best_sequence)-1, len(ref_sequence), dtype=int)\n", "    best_sequence_plot = best_sequence[indices]\n", "else:\n", "    best_sequence_plot = best_sequence\n", "\n", "plt.plot(ref_sequence, 'b-', alpha=0.7, label='参考序列', linewidth=1)\n", "plt.plot(best_sequence_plot, 'r-', label='最优序列', linewidth=2)\n", "plt.title('序列对比')\n", "plt.xlabel('时间步')\n", "plt.ylabel('温度 (°C)')\n", "plt.legend()\n", "plt.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"✓ 结果可视化完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. 性能分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化性能分析器\n", "performance_analyzer = PerformanceAnalyzer('../config/config.yaml')\n", "\n", "# 分析优化性能\n", "performance_metrics = performance_analyzer.analyze_optimization_performance(optimization_results)\n", "\n", "print(\"优化性能分析:\")\n", "print(\"\\n收敛性能:\")\n", "conv = performance_metrics['convergence']\n", "for key, value in conv.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "print(\"\\n多样性维持:\")\n", "div = performance_metrics['diversity']\n", "for key, value in div.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "print(\"\\n算法稳定性:\")\n", "stab = performance_metrics['stability']\n", "for key, value in stab.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "print(\"\\n算法效率:\")\n", "eff = performance_metrics['efficiency']\n", "for key, value in eff.items():\n", "    print(f\"  {key}: {value}\")\n", "\n", "# 生成性能报告\n", "report = performance_analyzer.generate_performance_report(optimization_results)\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"详细性能报告:\")\n", "print(\"=\"*60)\n", "print(report)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. 序列质量分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 初始化序列生成器用于质量分析\n", "sequence_generator = SequenceGenerator('../config/config.yaml')\n", "\n", "# 分析最优序列质量\n", "quality_analysis = sequence_generator.analyze_sequence_quality(best_sequence)\n", "\n", "print(\"最优序列质量分析:\")\n", "for key, value in quality_analysis.items():\n", "    if isinstance(value, dict):\n", "        print(f\"\\n{key}:\")\n", "        for sub_key, sub_value in value.items():\n", "            print(f\"  {sub_key}: {sub_value}\")\n", "    else:\n", "        print(f\"{key}: {value}\")\n", "\n", "# 验证约束满足情况\n", "validation_results = sequence_generator.validate_sequence(best_sequence)\n", "print(\"\\n约束验证结果:\")\n", "for constraint, satisfied in validation_results.items():\n", "    status = \"✓\" if satisfied else \"✗\"\n", "    print(f\"  {status} {constraint}: {satisfied}\")\n", "\n", "print(\"\\n✓ 序列质量分析完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. 总结和建议"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=\"*60)\n", "print(\"教程总结\")\n", "print(\"=\"*60)\n", "\n", "print(f\"\\n✓ 成功完成化工车间温度序列PSO优化\")\n", "print(f\"\\n关键结果:\")\n", "print(f\"  - 分类器测试准确率: {training_results['test_accuracy']:.4f}\")\n", "print(f\"  - 最佳适应度: {optimization_results['best_fitness']:.6f}\")\n", "print(f\"  - 优化迭代次数: {optimization_results['total_iterations']}\")\n", "print(f\"  - 最优序列长度: {len(best_sequence)}\")\n", "print(f\"  - 温度范围: [{best_sequence.min():.2f}, {best_sequence.max():.2f}]°C\")\n", "\n", "print(f\"\\n下一步建议:\")\n", "print(f\"  1. 调整PSO参数（粒子群大小、迭代次数）以获得更好的结果\")\n", "print(f\"  2. 尝试不同的适应度评估策略\")\n", "print(f\"  3. 增加数据增强以提高分类器性能\")\n", "print(f\"  4. 使用更多的参考序列进行比较\")\n", "print(f\"  5. 在实际化工过程中验证优化结果\")\n", "\n", "print(f\"\\n完整系统使用命令:\")\n", "print(f\"  python main.py --mode full --save-plots --max-iterations 200\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"教程完成！\")\n", "print(\"=\"*60)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.0"}}, "nbformat": 4, "nbformat_minor": 4}