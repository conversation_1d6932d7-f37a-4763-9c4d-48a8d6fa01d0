{"description": "基于Sample_1.xlsx真实温度数据的约束配置", "data_source": "Sample_1.xlsx详细分析结果", "analysis_date": "2025-07-21", "temperature_bounds": {"min": 15.0, "max": 155.0, "description": "基于真实数据范围16.20-151.30°C，留有安全边界"}, "initial_temperature": {"min": 15.0, "max": 20.0, "target": 16.4, "description": "真实数据起始温度16.40°C"}, "final_temperature": {"min": 140.0, "max": 145.0, "target": 142.8, "description": "真实数据最终温度142.80°C"}, "peak_temperature": {"min": 150.0, "max": 155.0, "target": 151.3, "description": "真实数据峰值温度151.30°C"}, "average_temperature": {"min": 130.0, "max": 135.0, "target": 131.97, "description": "真实数据平均温度131.97°C"}, "temperature_range": {"min": 130.0, "max": 140.0, "target": 135.1, "description": "真实数据温度变化幅度135.10°C"}, "total_change": {"min": 120.0, "max": 130.0, "target": 126.4, "description": "真实数据总体变化126.40°C"}, "stage_constraints": {"description": "基于真实数据的5阶段温度变化模式", "stage_1": {"name": "主要升温段", "data_range": [1, 4864], "start_temp": 16.4, "end_temp": 122.1, "change": 105.7, "rate": 0.0217, "characteristic": "快速升温"}, "stage_2": {"name": "缓慢升温段", "data_range": [4865, 9728], "start_temp": 122.1, "end_temp": 137.2, "change": 15.1, "rate": 0.0031, "characteristic": "缓慢升温"}, "stage_3": {"name": "温度稳定段", "data_range": [9729, 14592], "start_temp": 137.2, "end_temp": 139.0, "change": 1.8, "rate": 0.0004, "characteristic": "温度稳定"}, "stage_4": {"name": "轻微降温段", "data_range": [14593, 19456], "start_temp": 139.0, "end_temp": 136.8, "change": -2.2, "rate": -0.0005, "characteristic": "轻微降温"}, "stage_5": {"name": "最终升温段", "data_range": [19457, 24321], "start_temp": 136.8, "end_temp": 142.8, "change": 6.0, "rate": 0.0012, "characteristic": "最终升温"}}, "change_rate_constraints": {"average_rate": 0.0052, "max_heating_rate": 0.4, "max_cooling_rate": -0.2, "std_rate": 0.0422, "description": "基于真实数据的温度变化率约束"}, "distribution_constraints": {"heating_points_ratio": 0.079, "cooling_points_ratio": 0.042, "stable_points_ratio": 0.879, "description": "真实数据中87.9%为恒温点，7.9%为升温点，4.2%为降温点"}, "sequence_properties": {"total_length": 24321, "sampling_assumption": "等间隔采样", "trend": "整体升温过程", "description": "真实数据序列属性"}, "penalty_weights": {"initial_temp_violation": 1000.0, "final_temp_violation": 1000.0, "total_change_violation": 500.0, "stage_pattern_violation": 300.0, "rate_violation": 100.0, "description": "约束违反惩罚权重"}}