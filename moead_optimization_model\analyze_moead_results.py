#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析MOEA/D优化结果与真实温度数据的对比
"""

import pandas as pd
import numpy as np
import os
import glob

def analyze_optimization_results():
    """分析优化结果"""
    
    print("=== 分析MOEA/D优化结果与真实数据对比 ===")
    
    # 真实数据特征（作为参考标准）
    real_data_features = {
        'initial_temp': 16.40,
        'mid_temp': 137.20,
        'final_temp': 142.80,
        'peak_temp': 151.30,
        'min_temp': 16.20,
        'temp_range': 135.10,
        'total_change': 126.40,
        'trend': '升温过程'
    }
    
    print("真实数据特征（参考标准）:")
    for key, value in real_data_features.items():
        if isinstance(value, float):
            print(f"  {key}: {value:.2f}°C")
        else:
            print(f"  {key}: {value}")
    
    # 查找最新的优化结果文件
    result_files = glob.glob('results/constrained_pareto_solution_*_*.xlsx')
    if not result_files:
        print("未找到优化结果文件")
        return
    
    # 按时间戳排序，取最新的
    result_files.sort()
    latest_files = result_files[-5:]  # 分析最新的5个解
    
    print(f"\n找到 {len(result_files)} 个结果文件，分析最新的 {len(latest_files)} 个:")
    
    optimization_results = []
    
    for i, file_path in enumerate(latest_files):
        print(f"\n--- 分析文件 {i+1}: {os.path.basename(file_path)} ---")
        
        try:
            df = pd.read_excel(file_path)
            
            # 检查列名
            temp_col = None
            for col in df.columns:
                if '温度' in str(col) or 'temp' in str(col).lower():
                    temp_col = col
                    break
            
            if temp_col is None:
                print("未找到温度列")
                print("可用列:", df.columns.tolist())
                continue
            
            temp_data = df[temp_col]
            
            # 计算优化结果的温度特征
            opt_features = {
                'file': os.path.basename(file_path),
                'initial_temp': temp_data.iloc[0],
                'final_temp': temp_data.iloc[-1],
                'peak_temp': temp_data.max(),
                'min_temp': temp_data.min(),
                'mean_temp': temp_data.mean(),
                'temp_range': temp_data.max() - temp_data.min(),
                'total_change': temp_data.iloc[-1] - temp_data.iloc[0],
                'data_points': len(temp_data)
            }
            
            # 分析变化趋势
            temp_diff = temp_data.diff().dropna()
            opt_features.update({
                'avg_change_rate': temp_diff.mean(),
                'max_heating_rate': temp_diff.max(),
                'max_cooling_rate': temp_diff.min(),
                'heating_points': (temp_diff > 0).sum(),
                'cooling_points': (temp_diff < 0).sum(),
                'stable_points': (temp_diff == 0).sum()
            })
            
            # 计算与真实数据的差异
            differences = {
                'initial_temp_diff': abs(opt_features['initial_temp'] - real_data_features['initial_temp']),
                'final_temp_diff': abs(opt_features['final_temp'] - real_data_features['final_temp']),
                'peak_temp_diff': abs(opt_features['peak_temp'] - real_data_features['peak_temp']),
                'temp_range_diff': abs(opt_features['temp_range'] - real_data_features['temp_range']),
                'total_change_diff': abs(opt_features['total_change'] - real_data_features['total_change'])
            }
            
            opt_features.update(differences)
            
            # 获取目标函数值
            if 'f1_label1' in df.columns:
                opt_features['f1_label1'] = df['f1_label1'].iloc[0]
            if 'f2_label2' in df.columns:
                opt_features['f2_label2'] = df['f2_label2'].iloc[0]
            if 'f3_smoothness' in df.columns:
                opt_features['f3_smoothness'] = df['f3_smoothness'].iloc[0]
            if 'constraint_penalty' in df.columns:
                opt_features['constraint_penalty'] = df['constraint_penalty'].iloc[0]
            
            optimization_results.append(opt_features)
            
            # 输出分析结果
            print(f"数据点数: {opt_features['data_points']}")
            print(f"起始温度: {opt_features['initial_temp']:.2f}°C (差异: {differences['initial_temp_diff']:.2f}°C)")
            print(f"最终温度: {opt_features['final_temp']:.2f}°C (差异: {differences['final_temp_diff']:.2f}°C)")
            print(f"峰值温度: {opt_features['peak_temp']:.2f}°C (差异: {differences['peak_temp_diff']:.2f}°C)")
            print(f"温度范围: {opt_features['temp_range']:.2f}°C (差异: {differences['temp_range_diff']:.2f}°C)")
            print(f"总体变化: {opt_features['total_change']:.2f}°C (差异: {differences['total_change_diff']:.2f}°C)")
            
            # 判断趋势
            if opt_features['total_change'] > 10:
                trend = "升温过程"
            elif opt_features['total_change'] < -10:
                trend = "降温过程"
            else:
                trend = "温度稳定"
            print(f"整体趋势: {trend}")
            
            # 变化点分析
            total_points = opt_features['heating_points'] + opt_features['cooling_points'] + opt_features['stable_points']
            print(f"升温点: {opt_features['heating_points']} ({opt_features['heating_points']/total_points*100:.1f}%)")
            print(f"降温点: {opt_features['cooling_points']} ({opt_features['cooling_points']/total_points*100:.1f}%)")
            print(f"恒温点: {opt_features['stable_points']} ({opt_features['stable_points']/total_points*100:.1f}%)")
            
            # 目标函数值
            if 'f1_label1' in opt_features:
                print(f"f1_label1: {opt_features['f1_label1']:.6f}")
            if 'f2_label2' in opt_features:
                print(f"f2_label2: {opt_features['f2_label2']:.6f}")
            if 'f3_smoothness' in opt_features:
                print(f"f3_smoothness: {opt_features['f3_smoothness']:.6f}")
            if 'constraint_penalty' in opt_features:
                print(f"constraint_penalty: {opt_features['constraint_penalty']:.6f}")
                
        except Exception as e:
            print(f"分析文件 {file_path} 时出错: {e}")
    
    # 生成对比总结
    if optimization_results:
        print("\n" + "="*60)
        print("优化结果与真实数据对比总结")
        print("="*60)
        
        # 计算平均差异
        avg_diffs = {}
        for key in ['initial_temp_diff', 'final_temp_diff', 'peak_temp_diff', 'temp_range_diff', 'total_change_diff']:
            avg_diffs[key] = np.mean([result[key] for result in optimization_results])
        
        print("平均差异:")
        print(f"  起始温度差异: {avg_diffs['initial_temp_diff']:.2f}°C")
        print(f"  最终温度差异: {avg_diffs['final_temp_diff']:.2f}°C")
        print(f"  峰值温度差异: {avg_diffs['peak_temp_diff']:.2f}°C")
        print(f"  温度范围差异: {avg_diffs['temp_range_diff']:.2f}°C")
        print(f"  总变化差异: {avg_diffs['total_change_diff']:.2f}°C")
        
        # 识别主要问题
        print("\n主要问题识别:")
        problems = []
        
        if avg_diffs['initial_temp_diff'] > 5:
            problems.append(f"起始温度偏差过大 ({avg_diffs['initial_temp_diff']:.1f}°C)")
        
        if avg_diffs['final_temp_diff'] > 5:
            problems.append(f"最终温度偏差过大 ({avg_diffs['final_temp_diff']:.1f}°C)")
        
        if avg_diffs['total_change_diff'] > 20:
            problems.append(f"总体变化幅度偏差过大 ({avg_diffs['total_change_diff']:.1f}°C)")
        
        # 检查约束惩罚
        penalties = [result.get('constraint_penalty', 0) for result in optimization_results]
        avg_penalty = np.mean(penalties)
        if avg_penalty > 0:
            problems.append(f"存在约束违反 (平均惩罚: {avg_penalty:.2f})")
        
        if problems:
            for i, problem in enumerate(problems, 1):
                print(f"  {i}. {problem}")
        else:
            print("  未发现明显问题")
        
        return optimization_results, real_data_features, avg_diffs
    
    return None, None, None

if __name__ == "__main__":
    analyze_optimization_results()
