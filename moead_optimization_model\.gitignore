# Python缓存文件
__pycache__/
*.py[cod]
*$py.class

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# celery beat schedule file
celerybeat-schedule

# SageMath parsed files
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder项目设置
.spyderproject
.spyproject

# Rope项目设置
.ropeproject

# mkdocs文档
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# 模型文件（可选，根据需要调整）
# models/*.joblib
# *.pkl
# *.pickle

# 结果文件
results/
output/
logs/
*.log

# 临时文件
*.tmp
*.temp
*~
*.bak
*.swp
*.swo

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE文件
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# 实验数据（可选）
experiments/
weight_series_models/
models_*/

# 测试报告
test_report.txt
*.html
*.xml

# 训练历史
training_history.json
feature_importance.json

# 测试文件和临时脚本
test_*.py
*_test.py
check_*.py
quick_*.py
analyze_*.py
debug_*.py

# 图表和可视化文件
*.png
*.jpg
*.jpeg
*.gif
*.svg

# 特定结果文件（保留重要的温度序列文件）
results/*.png
results/*.npy
results/*.txt
results/optimization_*
results/best_sequence_*
# 保留Excel和CSV格式的温度序列文件
!results/best_temperature_sequence_*.xlsx
!results/best_temperature_sequence_*.csv
