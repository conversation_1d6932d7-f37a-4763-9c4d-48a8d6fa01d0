"""
Sample Data Driven Objective Functions for MOEAD Optimization

This module provides objective functions that evaluate temperature sequences
based on their similarity to real sample data patterns and statistical characteristics.
"""

import numpy as np
from typing import Dict, Optional
import logging
from .sample_data_analyzer import SampleDataAnalyzer
from .real_data_constraints import RealDataConstraints

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SampleDataDrivenObjectiveFunctions:
    """
    Objective functions based on sample data patterns and characteristics
    
    This class implements three main objectives:
    1. Statistical deviation minimization
    2. Pattern matching with reference sample
    3. Five-stage pattern compliance
    """
    
    def __init__(self, sample_analyzer: SampleDataAnalyzer, 
                 constraints: Optional[Dict] = None,
                 penalty_weight: float = 1000.0):
        """
        Initialize sample data driven objective functions
        
        Args:
            sample_analyzer: Analyzer containing sample data and statistics
            constraints: Constraint configuration (for compatibility)
            penalty_weight: Weight for constraint penalties (for compatibility)
        """
        self.sample_analyzer = sample_analyzer
        self.constraints = constraints
        self.penalty_weight = penalty_weight
        
        # Ensure sample data is loaded and analyzed
        if not self.sample_analyzer.sample_data:
            self.sample_analyzer.load_sample_data()
        if not self.sample_analyzer.sample_statistics:
            self.sample_analyzer.calculate_sample_statistics()
        
        # Get reference statistics and template
        self.overall_statistics = self.sample_analyzer.overall_statistics
        self.reference_template = self.sample_analyzer.get_reference_template()
        
        # Initialize constraint manager
        self.constraint_manager = RealDataConstraints()
        
        logger.info("Sample data driven objective functions initialized")
        logger.info(f"  - Reference template length: {len(self.reference_template)}")
        logger.info(f"  - Overall statistics available: {self.overall_statistics is not None}")
    
    def objective_1_statistical_deviation(self, temperature_sequence: np.ndarray) -> float:
        """
        Objective 1: Minimize deviation from sample statistical characteristics
        
        This objective evaluates how well a sequence matches the statistical
        properties observed in the sample data (initial temp, peak temp, etc.)
        
        Args:
            temperature_sequence: Temperature sequence to evaluate
            
        Returns:
            Statistical deviation score (lower is better)
        """
        try:
            # Calculate current sequence statistics
            current_stats = self.sample_analyzer._calculate_single_sample_statistics(
                temperature_sequence
            )
            
            deviations = []
            
            # 1. Initial temperature deviation (weight: 0.2)
            initial_target = self.overall_statistics.initial_temp.mean  # ~31.05°C
            initial_std = self.overall_statistics.initial_temp.std
            initial_deviation = abs(current_stats.initial_temp - initial_target) / (initial_std + 1e-6)
            deviations.append(0.2 * initial_deviation)
            
            # 2. Peak temperature deviation (weight: 0.3)
            peak_target = self.overall_statistics.peak_temp.mean  # ~147.8°C
            peak_std = self.overall_statistics.peak_temp.std
            peak_deviation = abs(current_stats.peak_temp - peak_target) / (peak_std + 1e-6)
            deviations.append(0.3 * peak_deviation)
            
            # 3. Peak position deviation (weight: 0.2)
            peak_pos_target = self.overall_statistics.peak_position.mean  # ~0.958
            peak_pos_std = self.overall_statistics.peak_position.std
            peak_pos_deviation = abs(current_stats.peak_position - peak_pos_target) / (peak_pos_std + 1e-6)
            deviations.append(0.2 * peak_pos_deviation)
            
            # 4. Total change deviation (weight: 0.15)
            total_change_target = self.overall_statistics.total_change.mean  # ~109.4°C
            total_change_std = self.overall_statistics.total_change.std
            total_change_deviation = abs(current_stats.total_change - total_change_target) / (total_change_std + 1e-6)
            deviations.append(0.15 * total_change_deviation)
            
            # 5. Final temperature deviation (weight: 0.15)
            final_target = self.overall_statistics.final_temp.mean  # ~140.4°C
            final_std = self.overall_statistics.final_temp.std
            final_deviation = abs(current_stats.final_temp - final_target) / (final_std + 1e-6)
            deviations.append(0.15 * final_deviation)
            
            # Total weighted deviation
            total_deviation = sum(deviations)
            
            return float(total_deviation)
            
        except Exception as e:
            logger.error(f"Error in objective 1 calculation: {e}")
            return 10.0  # Return high penalty for failed evaluation
    
    def objective_2_pattern_matching(self, temperature_sequence: np.ndarray) -> float:
        """
        Objective 2: Minimize difference from Sample_1 pattern
        
        This objective evaluates how closely a sequence follows the overall
        pattern and shape of the reference sample (Sample_1).
        
        Args:
            temperature_sequence: Temperature sequence to evaluate
            
        Returns:
            Pattern difference score (lower is better)
        """
        try:
            # Calculate similarity with reference template
            similarity_metrics = self.sample_analyzer.calculate_sequence_similarity(
                temperature_sequence, 'Sample_1'
            )
            
            # Convert similarity to difference (1 - similarity)
            pattern_difference = 1.0 - similarity_metrics.overall_similarity
            
            return float(pattern_difference)
            
        except Exception as e:
            logger.error(f"Error in objective 2 calculation: {e}")
            return 1.0  # Return maximum difference for failed evaluation
    
    def objective_3_stage_pattern_compliance(self, temperature_sequence: np.ndarray) -> float:
        """
        Objective 3: Minimize five-stage pattern violations
        
        This objective evaluates how well a sequence follows the expected
        five-stage temperature change pattern observed in sample data.
        
        Args:
            temperature_sequence: Temperature sequence to evaluate
            
        Returns:
            Stage pattern violation score (lower is better)
        """
        try:
            # Calculate current sequence stage changes
            current_stage_changes = self.sample_analyzer._calculate_stage_changes(
                temperature_sequence
            )
            
            # Expected stage patterns based on sample analysis
            # Stage 1: Main heating (~100.5°C), Stage 2: Gradual heating (~4.7°C)
            # Stage 3: Stable (~-0.8°C), Stage 4: Light cooling (~-2.0°C)
            # Stage 5: Final heating (~6.9°C)
            expected_patterns = [
                {'target': 100.5, 'tolerance': 40.0, 'type': 'heating'},    # Stage 1
                {'target': 4.7, 'tolerance': 15.0, 'type': 'gradual'},     # Stage 2
                {'target': -0.8, 'tolerance': 8.0, 'type': 'stable'},      # Stage 3
                {'target': -2.0, 'tolerance': 7.0, 'type': 'cooling'},     # Stage 4
                {'target': 6.9, 'tolerance': 10.0, 'type': 'final'}        # Stage 5
            ]
            
            violations = []
            
            for i, (current_change, pattern) in enumerate(zip(current_stage_changes, expected_patterns)):
                violation = 0.0
                
                if pattern['type'] == 'heating':  # Stage 1: Should have significant heating
                    if current_change < pattern['target'] * 0.6:  # At least 60% of expected heating
                        violation = (pattern['target'] * 0.6 - current_change) / pattern['target']
                
                elif pattern['type'] == 'gradual':  # Stage 2: Should have moderate change
                    if abs(current_change) > pattern['tolerance']:
                        violation = (abs(current_change) - pattern['tolerance']) / pattern['tolerance']
                
                elif pattern['type'] == 'stable':  # Stage 3: Should be relatively stable
                    if abs(current_change) > pattern['tolerance']:
                        violation = (abs(current_change) - pattern['tolerance']) / pattern['tolerance']
                
                elif pattern['type'] == 'cooling':  # Stage 4: Should not have significant heating
                    if current_change > 5.0:  # Avoid significant heating
                        violation = (current_change - 5.0) / 5.0
                
                elif pattern['type'] == 'final':  # Stage 5: Should not have significant cooling
                    if current_change < -5.0:  # Avoid significant cooling
                        violation = abs(current_change + 5.0) / 5.0
                
                violations.append(violation)
            
            # Total violation score
            total_violation = sum(violations)
            
            return float(total_violation)
            
        except Exception as e:
            logger.error(f"Error in objective 3 calculation: {e}")
            return 5.0  # Return high penalty for failed evaluation
    
    def evaluate(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        Evaluate all sample-driven objective functions
        
        Args:
            temperature_sequence: Temperature sequence to evaluate
            
        Returns:
            Dictionary containing all objective function values and penalties
        """
        # Calculate the three sample-driven objectives
        f1_statistical_deviation = self.objective_1_statistical_deviation(temperature_sequence)
        f2_pattern_difference = self.objective_2_pattern_matching(temperature_sequence)
        f3_stage_violation = self.objective_3_stage_pattern_compliance(temperature_sequence)
        
        # Calculate constraint penalty using real data constraints
        constraint_penalty = self.constraint_manager.calculate_constraint_penalty(temperature_sequence)
        
        # Apply light penalty to objectives (all are minimization problems)
        penalty_factor = 0.1  # Light penalty to maintain objective balance
        
        objectives = {
            'f1_statistical_deviation': f1_statistical_deviation + constraint_penalty * penalty_factor,
            'f2_pattern_difference': f2_pattern_difference + constraint_penalty * penalty_factor,
            'f3_stage_violation': f3_stage_violation + constraint_penalty * penalty_factor,
            'constraint_penalty': constraint_penalty
        }
        
        return objectives
    
    def evaluate_individual_objectives(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        Evaluate individual objectives without constraint penalties
        
        Args:
            temperature_sequence: Temperature sequence to evaluate
            
        Returns:
            Dictionary containing individual objective values
        """
        return {
            'f1_statistical_deviation': self.objective_1_statistical_deviation(temperature_sequence),
            'f2_pattern_difference': self.objective_2_pattern_matching(temperature_sequence),
            'f3_stage_violation': self.objective_3_stage_pattern_compliance(temperature_sequence)
        }
    
    def get_objective_descriptions(self) -> Dict[str, str]:
        """
        Get descriptions of all objectives
        
        Returns:
            Dictionary mapping objective names to descriptions
        """
        return {
            'f1_statistical_deviation': 'Minimize deviation from sample statistical characteristics',
            'f2_pattern_difference': 'Minimize difference from Sample_1 reference pattern',
            'f3_stage_violation': 'Minimize violations of five-stage temperature pattern',
            'constraint_penalty': 'Penalty for violating real data constraints'
        }
    
    def calculate_sample_compliance_score(self, temperature_sequence: np.ndarray) -> float:
        """
        Calculate overall compliance score with sample data
        
        Args:
            temperature_sequence: Temperature sequence to evaluate
            
        Returns:
            Compliance score (0-1, higher is better)
        """
        try:
            # Get individual objective scores
            objectives = self.evaluate_individual_objectives(temperature_sequence)
            
            # Convert to compliance scores (1 - normalized_objective)
            # Normalize objectives to [0, 1] range for meaningful combination
            f1_compliance = max(0.0, 1.0 - min(1.0, objectives['f1_statistical_deviation'] / 5.0))
            f2_compliance = max(0.0, 1.0 - objectives['f2_pattern_difference'])
            f3_compliance = max(0.0, 1.0 - min(1.0, objectives['f3_stage_violation'] / 3.0))
            
            # Weighted average compliance
            overall_compliance = (
                0.4 * f1_compliance +  # Statistical compliance
                0.4 * f2_compliance +  # Pattern compliance
                0.2 * f3_compliance    # Stage compliance
            )
            
            return float(overall_compliance)
            
        except Exception as e:
            logger.error(f"Error calculating compliance score: {e}")
            return 0.0