#!/usr/bin/env python3
"""
Test script for the enhanced SampleDataAnalyzer
"""

import sys
import os
sys.path.append('src')

from sample_data_analyzer import SampleDataAnalyzer
import numpy as np

def test_sample_analyzer():
    """Test the enhanced SampleDataAnalyzer functionality"""
    print("Testing Enhanced SampleDataAnalyzer...")
    
    # Initialize analyzer
    analyzer = SampleDataAnalyzer()
    
    # Test 1: Load sample data
    print("\n1. Testing sample data loading...")
    try:
        sample_data = analyzer.load_sample_data()
        print(f"✓ Loaded {len(sample_data)} samples")
        
        # Check if we have the expected samples
        expected_samples = [f"Sample_{i}" for i in range(1, 22)]
        loaded_samples = list(sample_data.keys())
        print(f"✓ Expected samples: {len(expected_samples)}, Loaded: {len(loaded_samples)}")
        
        # Show sample data info
        for sample_name, data in list(sample_data.items())[:3]:  # Show first 3
            print(f"  {sample_name}: {len(data)} points, range [{data.min():.1f}, {data.max():.1f}]")
            
    except Exception as e:
        print(f"✗ Error loading sample data: {e}")
        return False
    
    # Test 2: Calculate sample statistics
    print("\n2. Testing sample statistics calculation...")
    try:
        sample_stats = analyzer.calculate_sample_statistics()
        print(f"✓ Calculated statistics for {len(sample_stats)} samples")
        
        # Show Sample_1 statistics
        if 'Sample_1' in sample_stats:
            stats = sample_stats['Sample_1']
            print(f"  Sample_1 statistics:")
            print(f"    Initial temp: {stats.initial_temp:.1f}°C")
            print(f"    Final temp: {stats.final_temp:.1f}°C")
            print(f"    Peak temp: {stats.peak_temp:.1f}°C")
            print(f"    Peak position: {stats.peak_position:.3f}")
            print(f"    Total change: {stats.total_change:.1f}°C")
            print(f"    Stage changes: {[f'{x:.1f}' for x in stats.stage_changes]}")
            
    except Exception as e:
        print(f"✗ Error calculating statistics: {e}")
        return False
    
    # Test 3: Get reference template
    print("\n3. Testing reference template extraction...")
    try:
        template = analyzer.get_reference_template()
        print(f"✓ Reference template: {len(template)} points")
        print(f"  Range: [{template.min():.1f}, {template.max():.1f}]°C")
        
    except Exception as e:
        print(f"✗ Error getting reference template: {e}")
        return False
    
    # Test 4: Calculate sequence similarity
    print("\n4. Testing sequence similarity calculation...")
    try:
        # Test with Sample_1 itself (should have high similarity)
        sample_1_data = sample_data['Sample_1']
        similarity = analyzer.calculate_sequence_similarity(sample_1_data, 'Sample_1')
        
        print(f"✓ Sample_1 vs Sample_1 similarity:")
        print(f"  Correlation: {similarity.correlation:.3f}")
        print(f"  Euclidean similarity: {similarity.euclidean_similarity:.3f}")
        print(f"  Overall similarity: {similarity.overall_similarity:.3f}")
        
        # Test with another sample
        if 'Sample_2' in sample_data:
            similarity_2 = analyzer.calculate_sequence_similarity(sample_data['Sample_2'], 'Sample_1')
            print(f"✓ Sample_2 vs Sample_1 similarity:")
            print(f"  Overall similarity: {similarity_2.overall_similarity:.3f}")
            
    except Exception as e:
        print(f"✗ Error calculating similarity: {e}")
        return False
    
    # Test 5: Get sample summary
    print("\n5. Testing sample summary generation...")
    try:
        summary = analyzer.get_sample_summary()
        print(f"✓ Generated summary for {summary['total_samples']} samples")
        
        overall_stats = summary['overall_statistics']
        print(f"  Initial temp: {overall_stats['initial_temp']['mean']:.1f}±{overall_stats['initial_temp']['std']:.1f}°C")
        print(f"  Final temp: {overall_stats['final_temp']['mean']:.1f}±{overall_stats['final_temp']['std']:.1f}°C")
        print(f"  Peak temp: {overall_stats['peak_temp']['mean']:.1f}±{overall_stats['peak_temp']['std']:.1f}°C")
        
        # Show stage analysis
        stage_analysis = summary['stage_analysis']
        print(f"  Five-stage analysis:")
        for stage, data in stage_analysis.items():
            print(f"    {stage}: {data['mean_change']:.1f}±{data['std_change']:.1f}°C")
            
    except Exception as e:
        print(f"✗ Error generating summary: {e}")
        return False
    
    print("\n✓ All tests passed! SampleDataAnalyzer is working correctly.")
    return True

if __name__ == "__main__":
    success = test_sample_analyzer()
    sys.exit(0 if success else 1)