#!/usr/bin/env python3
"""
新的MOEAD目标函数实现

实现三个新的目标函数：
1. label_1目标函数（最小化问题）：使用label_1分类器模型进行预测
2. label_2目标函数（最大化转最小化问题）：使用label_2分类器模型进行预测  
3. 温度变化稳定性目标函数（最大化转最小化问题）：评估温度序列的稳定性
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import logging
import yaml
import joblib
import os
from datetime import datetime

try:
    from .business_data_analyzer import BusinessDataAnalyzer
    from .temperature_constraints import TemperatureConstraints
except ImportError:
    from business_data_analyzer import BusinessDataAnalyzer
    from temperature_constraints import TemperatureConstraints

logger = logging.getLogger(__name__)


class NewObjectiveFunctions:
    """新的目标函数实现类"""
    
    def __init__(self, config_path: str = "config/config.yaml", shared_business_analyzer=None):
        """
        初始化新的目标函数
        
        Args:
            config_path: 配置文件路径
            shared_business_analyzer: 共享的业务数据分析器实例（可选）
        """
        # 加载配置
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.config_path = config_path
        
        # 目标函数权重
        self.objective_weights = [1.0, 1.0, 1.0]  # 默认权重
        
        # 参考模板
        self.reference_template = "Sample_1"
        
        # 初始化分析器
        self.business_analyzer = shared_business_analyzer  # 使用共享实例（如果提供）
        self.constraint_manager = None
        
        # 分类器相关
        self.label1_classifier = None
        self.label2_classifier = None
        self.feature_extractor = None
        
        # 缓存
        self.cache_enabled = True
        self.objective_cache = {}
        
        # 初始化组件
        self._initialize_analyzers()
        self._load_classifiers()
        
        logger.info("新目标函数初始化完成")
        logger.info(f"目标权重: {self.objective_weights}")
        logger.info(f"参考模板: {self.reference_template}")
    
    def _initialize_analyzers(self, shared_business_analyzer=None):
        """初始化分析器组件"""
        if self.business_analyzer is None:
            if shared_business_analyzer is not None:
                self.business_analyzer = shared_business_analyzer
                logger.info("使用共享的业务数据分析器")
            else:
                self.business_analyzer = BusinessDataAnalyzer()
                logger.info("创建新的业务数据分析器实例")
        else:
            logger.info("使用已存在的业务数据分析器实例（共享模式）")

        if self.constraint_manager is None:
            # 传递共享的业务数据分析器给约束管理器
            self.constraint_manager = TemperatureConstraints(shared_business_analyzer=self.business_analyzer)
            logger.info("约束管理器已初始化（使用共享的业务数据分析器）")
    
    def _load_classifiers(self):
        """加载训练好的分类器模型"""
        try:
            models_dir = "models"
            
            # 加载特征提取器
            feature_extractor_path = os.path.join(models_dir, "feature_extractor.joblib")
            if os.path.exists(feature_extractor_path):
                self.feature_extractor = joblib.load(feature_extractor_path)
                logger.info(f"特征提取器已加载: {feature_extractor_path}")
            else:
                logger.warning(f"特征提取器文件不存在: {feature_extractor_path}")
            
            # 加载label1分类器
            label1_path = os.path.join(models_dir, "label1_classifier.joblib")
            if os.path.exists(label1_path):
                self.label1_classifier = joblib.load(label1_path)
                logger.info(f"Label1分类器已加载: {label1_path}")
            else:
                logger.warning(f"Label1分类器文件不存在: {label1_path}")
                # 尝试使用通用分类器作为替代
                general_classifier_path = os.path.join(models_dir, "sequence_classifier.joblib")
                if os.path.exists(general_classifier_path):
                    self.label1_classifier = joblib.load(general_classifier_path)
                    logger.info(f"使用通用分类器作为Label1分类器: {general_classifier_path}")
            
            # 加载label2分类器
            label2_path = os.path.join(models_dir, "label2_classifier.joblib")
            if os.path.exists(label2_path):
                self.label2_classifier = joblib.load(label2_path)
                logger.info(f"Label2分类器已加载: {label2_path}")
            else:
                logger.warning(f"Label2分类器文件不存在: {label2_path}")
                # 尝试使用通用分类器作为替代
                general_classifier_path = os.path.join(models_dir, "sequence_classifier.joblib")
                if os.path.exists(general_classifier_path):
                    self.label2_classifier = joblib.load(general_classifier_path)
                    logger.info(f"使用通用分类器作为Label2分类器: {general_classifier_path}")
                    
        except Exception as e:
            logger.error(f"加载分类器模型失败: {e}")
            raise
    
    def objective_1_label1_prediction(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数1：label_1分类器预测（最小化问题）
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            目标函数值（越小越好）
        """
        try:
            # 初始化分析器（如果需要）
            self._initialize_analyzers()
            
            # 确保数据已加载
            if not hasattr(self.business_analyzer, 'temperature_sequences') or not self.business_analyzer.temperature_sequences:
                logger.info("温度序列数据未加载，开始加载...")
                self.business_analyzer.load_all_temperature_data()
            else:
                logger.debug("使用已加载的温度序列数据（共享模式）")
            
            # 检查分类器和特征提取器
            if self.label1_classifier is None or self.feature_extractor is None:
                logger.error("Label1分类器或特征提取器未加载")
                return 1.0  # 返回较大的惩罚值
            
            # 获取参考序列
            reference_sequences = list(self.business_analyzer.temperature_sequences.values())
            if not reference_sequences:
                logger.error("没有可用的参考序列")
                return 1.0
            
            reference_sequence = reference_sequences[0]  # 使用第一个样本作为参考
            
            # 构造成对比较数据
            pair_data = [{
                'sequence_1': temperature_sequence,
                'sequence_2': reference_sequence,
                'label': 0  # 占位符
            }]
            
            # 提取特征
            features = self.feature_extractor.transform(pair_data)
            
            # 使用label1分类器进行预测
            prediction_proba = self.label1_classifier.predict_proba(features)
            
            # 获取序列1更好的概率（通常是第二列）
            if prediction_proba.shape[1] > 1:
                seq1_better_prob = float(prediction_proba[0, 1])
            else:
                seq1_better_prob = float(prediction_proba[0, 0])
            
            # 转换为最小化问题：1 - 概率
            # 如果序列质量好，概率高，目标函数值小
            objective_value = 1.0 - seq1_better_prob
            
            logger.debug(f"Label1预测概率: {seq1_better_prob:.4f}, 目标函数值: {objective_value:.4f}")
            
            return float(objective_value)
            
        except Exception as e:
            logger.error(f"Label1目标函数计算失败: {e}")
            return 1.0  # 返回较大的惩罚值
    
    def objective_2_label2_prediction(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数2：label_2分类器预测（最大化转最小化问题）
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            目标函数值（越小越好）
        """
        try:
            # 初始化分析器（如果需要）
            self._initialize_analyzers()
            
            # 确保数据已加载
            if not hasattr(self.business_analyzer, 'temperature_sequences') or not self.business_analyzer.temperature_sequences:
                logger.info("温度序列数据未加载，开始加载...")
                self.business_analyzer.load_all_temperature_data()
            else:
                logger.debug("使用已加载的温度序列数据（共享模式）")
            
            # 检查分类器和特征提取器
            if self.label2_classifier is None or self.feature_extractor is None:
                logger.error("Label2分类器或特征提取器未加载")
                return 1.0  # 返回较大的惩罚值
            
            # 获取参考序列
            reference_sequences = list(self.business_analyzer.temperature_sequences.values())
            if not reference_sequences:
                logger.error("没有可用的参考序列")
                return 1.0
            
            reference_sequence = reference_sequences[0]  # 使用第一个样本作为参考
            
            # 构造成对比较数据
            pair_data = [{
                'sequence_1': temperature_sequence,
                'sequence_2': reference_sequence,
                'label': 0  # 占位符
            }]
            
            # 提取特征
            features = self.feature_extractor.transform(pair_data)
            
            # 使用label2分类器进行预测
            prediction_proba = self.label2_classifier.predict_proba(features)
            
            # 获取序列1更好的概率（通常是第二列）
            if prediction_proba.shape[1] > 1:
                seq1_better_prob = float(prediction_proba[0, 1])
            else:
                seq1_better_prob = float(prediction_proba[0, 0])
            
            # 转换为最小化问题：1 - 概率
            # 对于label_2，如果是最大化问题，我们希望概率越高越好，所以目标函数值越小越好
            objective_value = 1.0 - seq1_better_prob
            
            logger.debug(f"Label2预测概率: {seq1_better_prob:.4f}, 目标函数值: {objective_value:.4f}")
            
            return float(objective_value)
            
        except Exception as e:
            logger.error(f"Label2目标函数计算失败: {e}")
            return 1.0  # 返回较大的惩罚值

    def objective_3_temperature_stability(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数3：温度变化稳定性（最大化转最小化问题）

        Args:
            temperature_sequence: 温度序列

        Returns:
            目标函数值（越小越好，表示稳定性越高）
        """
        try:
            # 计算一阶差分（变化率）
            diff1 = np.diff(temperature_sequence)

            # 计算二阶差分（加速度）
            diff2 = np.diff(temperature_sequence, n=2)

            # 稳定性指标1：变化率的标准差（越小越稳定）
            change_rate_std = np.std(diff1)

            # 稳定性指标2：变化率的方差（越小越稳定）
            change_rate_var = np.var(diff1)

            # 稳定性指标3：加速度的绝对值平均（越小越稳定）
            acceleration_mean = np.mean(np.abs(diff2))

            # 稳定性指标4：波动性指标（基于滑动窗口标准差）
            window_size = min(100, len(temperature_sequence) // 10)
            if window_size > 1:
                rolling_std = pd.Series(temperature_sequence).rolling(window=window_size).std()
                volatility = np.nanmean(rolling_std)
            else:
                volatility = change_rate_std

            # 稳定性指标5：平滑性指标（基于变化率方差的倒数）
            smoothness = 1.0 / (1.0 + change_rate_var)

            # 稳定性指标6：连续性指标（符号变化次数）
            sign_changes = np.sum(np.diff(np.sign(diff1)) != 0)
            continuity_penalty = sign_changes / len(diff1) if len(diff1) > 0 else 0

            # 综合稳定性评分（归一化到0-1范围）
            # 权重分配：变化率标准差(30%) + 波动性(25%) + 加速度(20%) + 连续性(15%) + 平滑性(10%)
            stability_score = (
                0.30 * min(1.0, change_rate_std / 10.0) +      # 变化率标准差权重
                0.25 * min(1.0, volatility / 10.0) +           # 波动性权重
                0.20 * min(1.0, acceleration_mean / 5.0) +     # 加速度权重
                0.15 * continuity_penalty +                    # 连续性惩罚权重
                0.10 * (1.0 - smoothness)                      # 平滑性权重（取反）
            )

            # 转换为最小化问题：稳定性越高，目标函数值越小
            objective_value = float(stability_score)

            logger.debug(f"稳定性指标 - 变化率std: {change_rate_std:.4f}, 波动性: {volatility:.4f}, "
                        f"加速度: {acceleration_mean:.4f}, 连续性惩罚: {continuity_penalty:.4f}, "
                        f"平滑性: {smoothness:.4f}, 目标函数值: {objective_value:.4f}")

            return objective_value

        except Exception as e:
            logger.error(f"温度稳定性目标函数计算失败: {e}")
            return 1.0  # 返回较大的惩罚值

    def evaluate_all_objectives(self, temperature_sequence: np.ndarray) -> List[float]:
        """
        评估所有三个新目标函数

        Args:
            temperature_sequence: 温度序列

        Returns:
            [f1, f2, f3] 三个目标函数值的列表
        """
        # 检查缓存
        if self.cache_enabled:
            cache_key = hash(temperature_sequence.tobytes())
            if cache_key in self.objective_cache:
                return self.objective_cache[cache_key]

        # 计算三个新目标函数
        f1_label1 = self.objective_1_label1_prediction(temperature_sequence)
        f2_label2 = self.objective_2_label2_prediction(temperature_sequence)
        f3_stability = self.objective_3_temperature_stability(temperature_sequence)

        objectives = [f1_label1, f2_label2, f3_stability]

        # 缓存结果
        if self.cache_enabled:
            self.objective_cache[cache_key] = objectives

        return objectives

    def evaluate_individual_objectives(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        评估单独的目标函数（不含约束惩罚）

        Args:
            temperature_sequence: 温度序列

        Returns:
            包含各目标函数值的字典
        """
        objectives = self.evaluate_all_objectives(temperature_sequence)

        return {
            'f1_label1_prediction': objectives[0],
            'f2_label2_prediction': objectives[1],
            'f3_temperature_stability': objectives[2]
        }

    def get_objective_descriptions(self) -> Dict[str, str]:
        """
        获取新目标函数描述

        Returns:
            目标函数描述字典
        """
        return {
            'f1_label1_prediction': '最小化Label1分类器预测值（质量越好，值越小）',
            'f2_label2_prediction': '最小化Label2分类器预测值（质量越好，值越小）',
            'f3_temperature_stability': '最小化温度变化不稳定性（稳定性越高，值越小）'
        }

    def clear_cache(self):
        """清空缓存"""
        self.objective_cache.clear()
        logger.info("新目标函数缓存已清空")

    def set_objective_weights(self, weights: List[float]):
        """
        设置目标函数权重

        Args:
            weights: 权重列表 [w1, w2, w3]
        """
        if len(weights) != 3:
            raise ValueError("权重列表必须包含3个元素")

        self.objective_weights = weights
        logger.info(f"目标函数权重已更新: {self.objective_weights}")

    def set_reference_template(self, template: str):
        """
        设置参考模板

        Args:
            template: 参考模板名称
        """
        self.reference_template = template
        logger.info(f"参考模板已更新: {self.reference_template}")
