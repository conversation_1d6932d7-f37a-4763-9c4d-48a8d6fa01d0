"""
Sample-Based Sequence Generator for MOEAD Optimization

This module provides sequence generation capabilities based on real sample data,
ensuring generated sequences follow the statistical patterns and characteristics
of the training samples.
"""

import numpy as np
from typing import List, Optional, Dict
import logging
from .sample_data_analyzer import SampleDataAnaly<PERSON>, OverallStatistics

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SampleBasedSequenceGenerator:
    """
    Generator for temperature sequences based on sample data patterns
    
    This class generates temperature sequences that follow the statistical
    characteristics and five-stage patterns observed in real sample data.
    """
    
    def __init__(self, sample_analyzer: SampleDataAnalyzer, 
                 noise_level: float = 0.05, 
                 template_weight: float = 0.7):
        """
        Initialize the sample-based sequence generator
        
        Args:
            sample_analyzer: Analyzer containing loaded sample data
            noise_level: Level of noise to add for diversity (0-1)
            template_weight: Weight of template influence (0-1, higher = more similar to template)
        """
        self.sample_analyzer = sample_analyzer
        self.noise_level = noise_level
        self.template_weight = template_weight
        
        # Ensure sample data is loaded
        if not self.sample_analyzer.sample_data:
            self.sample_analyzer.load_sample_data()
        if not self.sample_analyzer.sample_statistics:
            self.sample_analyzer.calculate_sample_statistics()
        
        # Get reference template and statistics
        self.reference_template = self.sample_analyzer.get_reference_template()
        self.overall_statistics = self.sample_analyzer.overall_statistics
        
        # Sequence parameters
        self.sequence_length = len(self.reference_template)
        self.min_temp = 13.1  # Based on overall sample analysis
        self.max_temp = 151.3  # Based on overall sample analysis
        
        logger.info(f"Sample-based sequence generator initialized")
        logger.info(f"  - Reference template length: {self.sequence_length}")
        logger.info(f"  - Noise level: {self.noise_level}")
        logger.info(f"  - Template weight: {self.template_weight}")
    
    def generate_sample_based_sequence(self, seed: Optional[int] = None) -> np.ndarray:
        """
        Generate a temperature sequence based on sample data patterns
        
        Args:
            seed: Random seed for reproducibility
            
        Returns:
            Generated temperature sequence following sample patterns
        """
        if seed is not None:
            np.random.seed(seed)
        
        # 1. Start with reference template
        base_sequence = self.reference_template.copy()
        
        # 2. Generate random variant following sample statistics
        random_sequence = self._generate_random_variant()
        
        # 3. Add controlled noise for diversity
        noise = np.random.normal(0, self.noise_level * np.std(base_sequence), 
                               len(base_sequence))
        
        # 4. Weighted combination of template, random variant, and noise
        generated_sequence = (
            self.template_weight * base_sequence +
            (1 - self.template_weight) * random_sequence +
            noise
        )
        
        # 5. Apply sample constraints to ensure validity
        generated_sequence = self._apply_sample_constraints(generated_sequence)
        
        return generated_sequence
    
    def _generate_random_variant(self) -> np.ndarray:
        """
        Generate a random sequence variant following sample statistics
        
        Returns:
            Random sequence following sample statistical patterns
        """
        # Sample key parameters from statistical distributions
        initial_temp = np.random.normal(
            self.overall_statistics.initial_temp.mean,
            self.overall_statistics.initial_temp.std
        )
        
        final_temp = np.random.normal(
            self.overall_statistics.final_temp.mean,
            self.overall_statistics.final_temp.std
        )
        
        peak_temp = np.random.normal(
            self.overall_statistics.peak_temp.mean,
            self.overall_statistics.peak_temp.std
        )
        
        peak_position = np.random.normal(
            self.overall_statistics.peak_position.mean,
            self.overall_statistics.peak_position.std
        )
        
        # Ensure parameters are within reasonable bounds
        initial_temp = np.clip(initial_temp, self.min_temp, 50.0)
        final_temp = np.clip(final_temp, 120.0, self.max_temp)
        peak_temp = np.clip(peak_temp, 140.0, self.max_temp)
        peak_position = np.clip(peak_position, 0.85, 0.99)
        
        # Generate five-stage sequence with these parameters
        return self._generate_five_stage_sequence(
            initial_temp, final_temp, peak_temp, peak_position
        )
    
    def _generate_five_stage_sequence(self, initial_temp: float, final_temp: float,
                                    peak_temp: float, peak_position: float) -> np.ndarray:
        """
        Generate temperature sequence following five-stage pattern
        
        Args:
            initial_temp: Starting temperature
            final_temp: Ending temperature
            peak_temp: Peak temperature
            peak_position: Relative position of peak (0-1)
            
        Returns:
            Temperature sequence following five-stage pattern
        """
        sequence = np.zeros(self.sequence_length)
        
        # Define stage boundaries (based on sample analysis)
        stage_boundaries = [
            0,
            int(self.sequence_length * 0.2),  # 20%
            int(self.sequence_length * 0.4),  # 40%
            int(self.sequence_length * 0.6),  # 60%
            int(self.sequence_length * 0.8),  # 80%
            self.sequence_length - 1          # 100%
        ]
        
        peak_index = int(peak_position * self.sequence_length)
        
        # Stage 1: Main heating phase (0-20%) - Average increase ~100.5°C
        stage1_end_temp = initial_temp + np.random.normal(100.5, 10.0)
        sequence[stage_boundaries[0]:stage_boundaries[1]] = np.linspace(
            initial_temp, stage1_end_temp, 
            stage_boundaries[1] - stage_boundaries[0]
        )
        
        # Stage 2: Gradual heating phase (20-40%) - Average increase ~4.7°C
        stage2_end_temp = stage1_end_temp + np.random.normal(4.7, 2.0)
        sequence[stage_boundaries[1]:stage_boundaries[2]] = np.linspace(
            stage1_end_temp, stage2_end_temp,
            stage_boundaries[2] - stage_boundaries[1]
        )
        
        # Stage 3: Stable phase (40-60%) - Average change ~-0.8°C
        stage3_end_temp = stage2_end_temp + np.random.normal(-0.8, 1.5)
        sequence[stage_boundaries[2]:stage_boundaries[3]] = np.linspace(
            stage2_end_temp, stage3_end_temp,
            stage_boundaries[3] - stage_boundaries[2]
        )
        
        # Stage 4: Light cooling phase (60-80%) - Average decrease ~-2.0°C
        stage4_end_temp = stage3_end_temp + np.random.normal(-2.0, 1.0)
        sequence[stage_boundaries[3]:stage_boundaries[4]] = np.linspace(
            stage3_end_temp, stage4_end_temp,
            stage_boundaries[4] - stage_boundaries[3]
        )
        
        # Stage 5: Final heating phase (80-100%) - Average increase ~6.9°C
        # Handle peak temperature in this stage
        if peak_index >= stage_boundaries[4]:
            # Peak occurs in stage 5
            sequence[stage_boundaries[4]:peak_index] = np.linspace(
                stage4_end_temp, peak_temp,
                peak_index - stage_boundaries[4]
            )
            sequence[peak_index:] = np.linspace(
                peak_temp, final_temp,
                len(sequence) - peak_index
            )
        else:
            # Peak occurs earlier, just heat to final temperature
            sequence[stage_boundaries[4]:] = np.linspace(
                stage4_end_temp, final_temp,
                len(sequence) - stage_boundaries[4]
            )
        
        return sequence
    
    def _apply_sample_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        Apply constraints based on sample data to ensure sequence validity
        
        Args:
            sequence: Input temperature sequence
            
        Returns:
            Constrained sequence following sample patterns
        """
        # 1. Apply temperature bounds
        sequence = np.clip(sequence, self.min_temp, self.max_temp)
        
        # 2. Ensure initial temperature is within sample range
        initial_range = [
            self.overall_statistics.initial_temp.min,
            self.overall_statistics.initial_temp.max
        ]
        if sequence[0] < initial_range[0] or sequence[0] > initial_range[1]:
            sequence[0] = np.random.uniform(initial_range[0], initial_range[1])
        
        # 3. Ensure final temperature is within sample range
        final_range = [
            self.overall_statistics.final_temp.min,
            self.overall_statistics.final_temp.max
        ]
        if sequence[-1] < final_range[0] or sequence[-1] > final_range[1]:
            sequence[-1] = np.random.uniform(final_range[0], final_range[1])
        
        # 4. Ensure peak temperature and position are reasonable
        peak_idx = np.argmax(sequence)
        peak_position = peak_idx / len(sequence)
        
        # Adjust peak position if outside expected range
        if peak_position < 0.85 or peak_position > 0.99:
            new_peak_idx = int(np.random.uniform(0.85, 0.99) * len(sequence))
            peak_temp = sequence[peak_idx]
            
            # Move peak to new position
            sequence[new_peak_idx] = peak_temp
            
            # Smooth the old peak position
            if 0 < peak_idx < len(sequence) - 1:
                sequence[peak_idx] = (sequence[peak_idx-1] + sequence[peak_idx+1]) / 2
            else:
                sequence[peak_idx] = peak_temp * 0.9
        
        # 5. Apply light smoothing while preserving key features
        sequence = self._smooth_sequence(sequence)
        
        return sequence
    
    def _smooth_sequence(self, sequence: np.ndarray) -> np.ndarray:
        """
        Apply light smoothing to sequence while preserving key features
        
        Args:
            sequence: Input sequence
            
        Returns:
            Smoothed sequence
        """
        # Use moving average with small window
        window_size = 3
        smoothed = sequence.copy()
        
        for i in range(window_size, len(sequence) - window_size):
            smoothed[i] = np.mean(sequence[i-window_size:i+window_size+1])
        
        # Preserve endpoints
        smoothed[0] = sequence[0]
        smoothed[-1] = sequence[-1]
        
        return smoothed
    
    def generate_population(self, population_size: int) -> List[np.ndarray]:
        """
        Generate initial population based on sample data
        
        Args:
            population_size: Number of sequences to generate
            
        Returns:
            List of temperature sequences following sample patterns
        """
        population = []
        
        logger.info(f"Generating sample-based population of size {population_size}")
        
        for i in range(population_size):
            # Use different seeds for diversity
            sequence = self.generate_sample_based_sequence(seed=i)
            population.append(sequence)
        
        logger.info(f"Generated {len(population)} sample-based sequences")
        return population
    
    def validate_sequence_compliance(self, sequence: np.ndarray) -> Dict[str, float]:
        """
        Validate how well a sequence complies with sample patterns
        
        Args:
            sequence: Temperature sequence to validate
            
        Returns:
            Dictionary of compliance metrics
        """
        compliance = {}
        
        # Check statistical compliance
        stats = self.sample_analyzer._calculate_single_sample_statistics(sequence)
        
        # Initial temperature compliance
        initial_deviation = abs(stats.initial_temp - self.overall_statistics.initial_temp.mean)
        initial_compliance = max(0.0, 1.0 - initial_deviation / 
                               (self.overall_statistics.initial_temp.max - 
                                self.overall_statistics.initial_temp.min))
        compliance['initial_temp_compliance'] = initial_compliance
        
        # Final temperature compliance
        final_deviation = abs(stats.final_temp - self.overall_statistics.final_temp.mean)
        final_compliance = max(0.0, 1.0 - final_deviation / 
                             (self.overall_statistics.final_temp.max - 
                              self.overall_statistics.final_temp.min))
        compliance['final_temp_compliance'] = final_compliance
        
        # Peak temperature compliance
        peak_deviation = abs(stats.peak_temp - self.overall_statistics.peak_temp.mean)
        peak_compliance = max(0.0, 1.0 - peak_deviation / 
                            (self.overall_statistics.peak_temp.max - 
                             self.overall_statistics.peak_temp.min))
        compliance['peak_temp_compliance'] = peak_compliance
        
        # Peak position compliance
        peak_pos_deviation = abs(stats.peak_position - self.overall_statistics.peak_position.mean)
        peak_pos_compliance = max(0.0, 1.0 - peak_pos_deviation / 
                                (self.overall_statistics.peak_position.max - 
                                 self.overall_statistics.peak_position.min))
        compliance['peak_position_compliance'] = peak_pos_compliance
        
        # Overall compliance
        compliance['overall_compliance'] = np.mean([
            initial_compliance, final_compliance, 
            peak_compliance, peak_pos_compliance
        ])
        
        return compliance