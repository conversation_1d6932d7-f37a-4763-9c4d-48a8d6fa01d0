#!/usr/bin/env python3
"""
数据处理模块：构建成对比较数据集

该模块负责：
1. 加载温度序列数据和质量标签
2. 构建成对比较数据集用于训练分类器
3. 数据预处理和增强
"""

import pandas as pd
import numpy as np
import os
from typing import List, Tuple, Dict, Optional
import logging
from itertools import combinations
import yaml

logger = logging.getLogger(__name__)


class DataProcessor:
    """数据处理器：构建成对比较数据集"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化数据处理器
        
        Args:
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.data_config = self.config['data']
        self.data_dir = self.data_config['data_dir']
        
        # 存储加载的数据
        self.temperature_sequences = {}
        self.quality_labels = {}
        self.pairwise_dataset = []
        
    def load_temperature_sequences(self, sample_ids: Optional[List[int]] = None) -> Dict[int, np.ndarray]:
        """
        加载温度序列数据
        
        Args:
            sample_ids: 要加载的样本ID列表，如果为None则加载所有样本
            
        Returns:
            字典，键为样本ID，值为温度序列数组
        """
        if sample_ids is None:
            sample_ids = list(range(1, 22))  # 默认加载样本1-21
            
        sequences = {}
        
        for sample_id in sample_ids:
            sample_file = os.path.join(
                self.data_dir, 
                self.data_config['sample_file_pattern'].format(sample_id)
            )
            
            try:
                # 读取Excel文件
                df = pd.read_excel(sample_file, header=None)
                temperature_sequence = df.iloc[:, 0].values
                
                # 数据清洗
                temperature_sequence = self._clean_sequence(temperature_sequence)
                
                if len(temperature_sequence) > 0:
                    sequences[sample_id] = temperature_sequence
                    logger.info(f"成功加载样本 {sample_id}，序列长度: {len(temperature_sequence)}")
                else:
                    logger.warning(f"样本 {sample_id} 数据为空")
                    
            except Exception as e:
                logger.error(f"加载样本 {sample_id} 失败: {e}")
                continue
                
        self.temperature_sequences = sequences
        logger.info(f"总共加载了 {len(sequences)} 个温度序列")
        return sequences
    
    def load_quality_labels(self) -> Dict[str, np.ndarray]:
        """
        加载质量标签数据 (简化为只加载label_1)

        Returns:
            包含label_1标签的字典
        """
        labels = {}

        # 只加载label_1
        label_name = 'label_1'
        filename = self.data_config['label_files'][label_name]
        label_file = os.path.join(self.data_dir, filename)

        try:
            df = pd.read_excel(label_file, header=None)
            label_values = df.iloc[:, 0].values
            labels[label_name] = label_values

            logger.info(f"成功加载 {label_name}，数量: {len(label_values)}, "
                       f"范围: [{label_values.min():.4f}, {label_values.max():.4f}]")

        except Exception as e:
            logger.error(f"加载标签 {label_name} 失败: {e}")
            raise

        self.quality_labels = labels
        return labels
    
    def calculate_quality_score(self, sample_id: int, custom_weights: dict = None) -> float:
        """
        计算样本的综合质量评分

        Args:
            sample_id: 样本ID (1-based)
            custom_weights: 自定义权重字典，如果提供则覆盖配置文件中的权重

        Returns:
            综合质量评分
        """
        if not self.quality_labels:
            raise ValueError("质量标签尚未加载")

        # 转换为0-based索引
        idx = sample_id - 1

        # 获取权重（优先使用自定义权重）
        weights = custom_weights if custom_weights is not None else self.data_config['quality_weights']
        
        # 只使用label_1计算评分
        label_name = 'label_1'
        if label_name not in self.quality_labels:
            raise ValueError(f"标签 {label_name} 未找到")

        label_values = self.quality_labels[label_name]
        weight = weights.get(label_name, 1.0)

        # label_1: 越低越好，所以需要反转
        normalized_value = (label_values.max() - label_values[idx]) / (label_values.max() - label_values.min())
        score = weight * normalized_value

        return score

    def set_quality_weights(self, label1_weight: float):
        """
        设置质量评分权重 (简化为只使用label_1)

        Args:
            label1_weight: label_1的权重
        """
        # 验证权重
        if label1_weight < 0:
            raise ValueError("权重必须为非负数")

        if label1_weight == 0:
            raise ValueError("权重必须大于0")

        # 更新权重
        self.data_config['quality_weights'] = {
            'label_1': label1_weight
        }

        logger.info(f"质量权重已更新: label_1={label1_weight}")

    def create_pairwise_dataset(self, custom_weights: dict = None) -> List[Dict]:
        """
        创建成对比较数据集
        
        Returns:
            成对比较数据集列表，每个元素包含：
            - sample_id_1: 第一个样本ID
            - sample_id_2: 第二个样本ID  
            - sequence_1: 第一个温度序列
            - sequence_2: 第二个温度序列
            - label: 比较标签 (1表示序列1优于序列2，0表示相反)
            - quality_score_1: 第一个样本的质量评分
            - quality_score_2: 第二个样本的质量评分
        """
        if not self.temperature_sequences or not self.quality_labels:
            raise ValueError("数据尚未加载，请先调用load_temperature_sequences和load_quality_labels")
        
        pairwise_data = []
        sample_ids = list(self.temperature_sequences.keys())
        
        # 生成所有可能的样本对
        for id1, id2 in combinations(sample_ids, 2):
            # 计算质量评分（使用自定义权重如果提供）
            score1 = self.calculate_quality_score(id1, custom_weights)
            score2 = self.calculate_quality_score(id2, custom_weights)
            
            # 确定比较标签
            label = 1 if score1 > score2 else 0
            
            # 创建数据对
            pair_data = {
                'sample_id_1': id1,
                'sample_id_2': id2,
                'sequence_1': self.temperature_sequences[id1],
                'sequence_2': self.temperature_sequences[id2],
                'label': label,
                'quality_score_1': score1,
                'quality_score_2': score2,
                'score_diff': abs(score1 - score2)
            }
            
            pairwise_data.append(pair_data)
        
        self.pairwise_dataset = pairwise_data
        logger.info(f"创建了 {len(pairwise_data)} 个成对比较样本")
        
        # 统计标签分布
        labels = [pair['label'] for pair in pairwise_data]
        label_counts = np.bincount(labels)
        logger.info(f"标签分布: 类别0={label_counts[0]}, 类别1={label_counts[1]}")
        
        return pairwise_data
    
    def _clean_sequence(self, sequence: np.ndarray) -> np.ndarray:
        """
        清洗温度序列数据
        
        Args:
            sequence: 原始温度序列
            
        Returns:
            清洗后的温度序列
        """
        # 移除NaN值
        sequence = sequence[~np.isnan(sequence)]
        
        # 移除异常值（使用IQR方法）
        if len(sequence) > 0:
            Q1 = np.percentile(sequence, 25)
            Q3 = np.percentile(sequence, 75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # 保留在合理范围内的值
            sequence = sequence[(sequence >= lower_bound) & (sequence <= upper_bound)]
        
        # 长度限制
        max_length = self.data_config.get('max_sequence_length', 50000)
        if len(sequence) > max_length:
            # 等间隔采样
            indices = np.linspace(0, len(sequence)-1, max_length, dtype=int)
            sequence = sequence[indices]
        
        return sequence
    
    def get_dataset_statistics(self) -> Dict:
        """
        获取数据集统计信息
        
        Returns:
            包含统计信息的字典
        """
        if not self.temperature_sequences:
            return {}
        
        # 序列长度统计
        lengths = [len(seq) for seq in self.temperature_sequences.values()]
        
        # 温度范围统计
        all_temps = np.concatenate(list(self.temperature_sequences.values()))
        
        # 质量评分统计
        quality_scores = []
        for sample_id in self.temperature_sequences.keys():
            score = self.calculate_quality_score(sample_id)
            quality_scores.append(score)
        
        stats = {
            'sample_count': len(self.temperature_sequences),
            'sequence_length': {
                'min': min(lengths),
                'max': max(lengths),
                'mean': np.mean(lengths),
                'std': np.std(lengths)
            },
            'temperature_range': {
                'min': all_temps.min(),
                'max': all_temps.max(),
                'mean': all_temps.mean(),
                'std': all_temps.std()
            },
            'quality_scores': {
                'min': min(quality_scores),
                'max': max(quality_scores),
                'mean': np.mean(quality_scores),
                'std': np.std(quality_scores)
            },
            'pairwise_samples': len(self.pairwise_dataset) if self.pairwise_dataset else 0
        }
        
        return stats


    def augment_pairwise_data(self, augmentation_factor: int = 2, noise_level: float = 0.01) -> List[Dict]:
        """
        数据增强：为成对比较数据集添加噪声变体

        Args:
            augmentation_factor: 增强倍数
            noise_level: 噪声水平

        Returns:
            增强后的成对比较数据集
        """
        if not self.pairwise_dataset:
            raise ValueError("成对比较数据集尚未创建")

        augmented_data = self.pairwise_dataset.copy()

        for _ in range(augmentation_factor - 1):
            for pair in self.pairwise_dataset:
                # 为序列添加高斯噪声
                seq1_noisy = self._add_noise(pair['sequence_1'], noise_level)
                seq2_noisy = self._add_noise(pair['sequence_2'], noise_level)

                # 创建增强样本
                augmented_pair = pair.copy()
                augmented_pair['sequence_1'] = seq1_noisy
                augmented_pair['sequence_2'] = seq2_noisy

                augmented_data.append(augmented_pair)

        logger.info(f"数据增强完成，从 {len(self.pairwise_dataset)} 增加到 {len(augmented_data)} 个样本")
        return augmented_data

    def _add_noise(self, sequence: np.ndarray, noise_level: float) -> np.ndarray:
        """
        为序列添加高斯噪声

        Args:
            sequence: 原始序列
            noise_level: 噪声水平（相对于序列标准差）

        Returns:
            添加噪声后的序列
        """
        noise = np.random.normal(0, noise_level * np.std(sequence), len(sequence))
        return sequence + noise

    def save_processed_data(self, output_path: str):
        """
        保存处理后的数据

        Args:
            output_path: 输出文件路径
        """
        if not self.pairwise_dataset:
            raise ValueError("没有可保存的数据")

        # 准备保存的数据
        save_data = {
            'temperature_sequences': {str(k): v.tolist() for k, v in self.temperature_sequences.items()},
            'quality_labels': {k: v.tolist() for k, v in self.quality_labels.items()},
            'pairwise_dataset': self.pairwise_dataset,
            'statistics': self.get_dataset_statistics()
        }

        # 保存为numpy格式
        np.save(output_path, save_data)
        logger.info(f"数据已保存到 {output_path}")


def main():
    """测试数据处理功能"""
    processor = DataProcessor()

    # 加载数据
    sequences = processor.load_temperature_sequences()
    labels = processor.load_quality_labels()

    # 创建成对比较数据集
    pairwise_data = processor.create_pairwise_dataset()

    # 打印统计信息
    stats = processor.get_dataset_statistics()
    print("数据集统计信息:")
    for key, value in stats.items():
        print(f"{key}: {value}")

    # 测试数据增强
    augmented_data = processor.augment_pairwise_data(augmentation_factor=2, noise_level=0.01)
    print(f"增强后数据集大小: {len(augmented_data)}")


if __name__ == "__main__":
    main()
