#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于训练数据统计特征的约束管理器
用于确保优化生成的温度序列与训练数据在统计特征上保持一致
"""

import numpy as np
import json
import logging
from typing import Dict, List, Tuple, Any, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class TrainingDataConstraints:
    """基于训练数据统计特征的约束管理器"""
    
    def __init__(self, constraints_file: str = "range_based_training_constraints.json"):
        """
        初始化约束管理器

        Args:
            constraints_file: 训练数据分析结果文件路径
        """
        self.constraints_file = constraints_file
        self.constraints = {}
        self.load_constraints()
        
    def load_constraints(self):
        """加载约束参数"""
        try:
            # 首先尝试加载我们的温度约束分析结果
            analysis_file = "temperature_constraints_analysis.json"
            if Path(analysis_file).exists():
                with open(analysis_file, 'r', encoding='utf-8') as f:
                    analysis_data = json.load(f)
                    self._set_constraints_from_analysis(analysis_data)
                logger.info(f"成功从温度约束分析加载约束参数: {analysis_file}")
            # 然后尝试加载阶段性温度分析结果
            elif Path("phase_temperature_analysis.json").exists():
                with open("phase_temperature_analysis.json", 'r', encoding='utf-8') as f:
                    phase_data = json.load(f)
                    self._set_constraints_from_phase_analysis(phase_data)
                logger.info(f"成功从阶段性分析加载约束参数: phase_temperature_analysis.json")
            elif Path(self.constraints_file).exists():
                with open(self.constraints_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 检查是否是新的约束文件格式
                    if 'constraint_parameters' in data:
                        self.constraints = data.get('constraint_parameters', {})
                    else:
                        # 直接使用约束数据（新格式）
                        self.constraints = data
                logger.info(f"成功加载训练数据约束参数: {self.constraints_file}")
            else:
                logger.warning(f"约束文件不存在，使用默认约束")
                self._set_default_constraints()
        except Exception as e:
            logger.error(f"加载约束参数失败: {e}，使用默认约束")
            self._set_default_constraints()

    def _set_constraints_from_analysis(self, analysis_data: Dict[str, Any]):
        """基于温度约束分析设置约束参数"""
        constraints_data = analysis_data.get('constraints', {})

        # 提取约束范围
        start_temp_range = constraints_data.get('start_temp_range', [13.5, 102.3])
        global_temp_range = constraints_data.get('global_temp_range', [13.1, 151.3])
        initial_stage_range = constraints_data.get('initial_stage_range', [13.1, 143.5])
        middle_stage_range = constraints_data.get('middle_stage_range', [121.8, 141.2])
        final_stage_range = constraints_data.get('final_stage_range', [116.6, 151.3])

        self.constraints = {
            'temperature_bounds': {
                'min': global_temp_range[0],
                'max': global_temp_range[1],
                'safety_margin': 2.0
            },
            'starting_temperature_constraints': {
                'min': start_temp_range[0],
                'max': start_temp_range[1],
                'mean': (start_temp_range[0] + start_temp_range[1]) / 2,
                'std': (start_temp_range[1] - start_temp_range[0]) / 6,  # 假设3σ覆盖范围
                'starting_phase_mean_range': initial_stage_range
            },
            'gradient_constraints': {
                'overall_rise_rate_max': 0.001,
                'max_single_step_change': 0.5,
                'max_decrease_rate': 0.0006
            },
            'stage_constraints': {
                'initial_stage_range': initial_stage_range,
                'middle_stage_range': middle_stage_range,
                'final_stage_range': final_stage_range
            }
        }

        logger.info(f"从分析数据设置约束: 起始温度 {start_temp_range}, 全局范围 {global_temp_range}")

    def _set_constraints_from_phase_analysis(self, phase_data: Dict[str, Any]):
        """基于阶段性分析设置约束参数"""
        starting = phase_data.get('starting_temperatures', {})
        rise_patterns = phase_data.get('temperature_rise_patterns', {})
        phase_patterns = phase_data.get('detailed_phase_patterns', {})

        # 起始温度约束
        first_temp = starting.get('first_temperature', {})
        starting_phase = starting.get('starting_phase_mean', {})

        # 温度上升模式约束
        overall_rise = rise_patterns.get('summary', {}).get('overall', {})

        # 阶段性约束
        phase_summary = phase_patterns.get('summary', {})

        self.constraints = {
            'temperature_bounds': {
                'min': first_temp.get('min', 13.1),  # 修复：使用训练数据的实际最小值
                'max': first_temp.get('max', 151.3),  # 修复：使用训练数据的实际最大值
                'safety_margin': 2.0
            },
            'starting_temperature_constraints': {
                'min': first_temp.get('min', 13.5),  # 修复：使用训练数据的实际起始温度最小值
                'max': first_temp.get('max', 102.3),  # 修复：使用训练数据的实际起始温度最大值
                'mean': first_temp.get('mean', 117.5),
                'std': first_temp.get('std', 9.3),
                'starting_phase_mean_range': [
                    starting_phase.get('min', 13.1),  # 修复：使用训练数据的实际范围
                    starting_phase.get('max', 143.5)  # 修复：使用训练数据的实际范围
                ]
            },
            'gradient_constraints': {
                'max_abs_gradient': overall_rise.get('max_gradient', {}).get('mean', 0.124),
                'mean_abs_gradient_range': [0.0001, 0.002],  # 极慢的上升
                'overall_rise_rate_max': overall_rise.get('overall_rise_rate', {}).get('mean', 0.001) * 2,
                'positive_gradient_max': overall_rise.get('mean_positive_gradient', {}).get('mean', 0.1)
            },
            'phase_constraints': {},
            'smoothness_constraints': {
                'min_smoothness': 0.95,  # 要求极高的平滑性
                'max_second_diff_var': 0.01  # 极小的二阶差分方差
            }
        }

        # 设置详细的阶段约束
        phase_names = ['initial', 'early_rise', 'mid_rise', 'late_rise', 'plateau', 'final']
        phase_positions = [(0, 0.05), (0.05, 0.15), (0.15, 0.4), (0.4, 0.7), (0.7, 0.9), (0.9, 1.0)]

        for phase_name, (start_pct, end_pct) in zip(phase_names, phase_positions):
            if phase_name in phase_summary:
                phase_data = phase_summary[phase_name]
                self.constraints['phase_constraints'][phase_name] = {
                    'position': (start_pct, end_pct),
                    'temperature_range': [
                        phase_data['temperature']['min'] - 5.0,
                        phase_data['temperature']['max'] + 5.0
                    ],
                    'target_temperature': phase_data['temperature']['mean'],
                    'max_gradient': abs(phase_data['gradient']['mean']) + 2 * phase_data['gradient']['std'],
                    'max_variability': phase_data['variability']['mean'] + phase_data['variability']['std']
                }

    def _set_default_constraints(self):
        """设置默认约束参数 - 基于训练数据分析更新"""
        self.constraints = {
            'temperature_bounds': {'min': 13.1, 'max': 151.3, 'safety_margin': 2.0},  # 修复：使用训练数据范围
            'starting_temperature_constraints': {
                'min': 13.5, 'max': 102.3, 'mean': 117.5, 'std': 9.3,  # 修复：使用训练数据起始温度范围
                'starting_phase_mean_range': [13.1, 143.5]  # 修复：使用训练数据起始阶段范围
            },
            'gradient_constraints': {
                'max_abs_gradient': 0.124,
                'mean_abs_gradient_range': [0.0001, 0.002],
                'overall_rise_rate_max': 0.001,
                'positive_gradient_max': 0.1
            },
            'phase_constraints': {
                'initial': {
                    'position': (0, 0.05),
                    'temperature_range': [115.0, 130.0],
                    'target_temperature': 122.9,
                    'max_gradient': 0.01,
                    'max_variability': 8.0
                }
            },
            'smoothness_constraints': {
                'min_smoothness': 0.95,
                'max_second_diff_var': 0.01
            }
        }
    
    def apply_temperature_bounds(self, temperature_sequence: np.ndarray) -> np.ndarray:
        """
        应用温度边界约束
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            约束后的温度序列
        """
        bounds = self.constraints.get('temperature_bounds', {})
        min_temp = bounds.get('min', 95.0)
        max_temp = bounds.get('max', 151.0)
        
        # 应用硬边界约束
        constrained_sequence = np.clip(temperature_sequence, min_temp, max_temp)
        
        # 记录约束应用情况
        violations = np.sum((temperature_sequence < min_temp) | (temperature_sequence > max_temp))
        if violations > 0:
            logger.debug(f"温度边界约束修正了 {violations} 个点")
        
        return constrained_sequence
    
    def check_statistical_constraints(self, temperature_sequence: np.ndarray) -> Dict[str, bool]:
        """
        检查统计约束
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            约束检查结果
        """
        stats_constraints = self.constraints.get('statistical_constraints', {})
        
        # 计算序列统计特征
        seq_mean = np.mean(temperature_sequence)
        seq_std = np.std(temperature_sequence)
        
        # 检查约束
        mean_range = stats_constraints.get('mean_range', [120.0, 150.0])
        std_range = stats_constraints.get('std_range', [3.0, 15.0])
        
        results = {
            'mean_valid': mean_range[0] <= seq_mean <= mean_range[1],
            'std_valid': std_range[0] <= seq_std <= std_range[1],
            'seq_mean': seq_mean,
            'seq_std': seq_std,
            'target_mean': stats_constraints.get('target_mean', 134.7),
            'target_std': stats_constraints.get('target_std', 6.3)
        }
        
        return results
    
    def apply_statistical_constraints(self, temperature_sequence: np.ndarray, 
                                    strength: float = 0.5) -> np.ndarray:
        """
        应用统计约束（基于范围的软约束版本）
        
        Args:
            temperature_sequence: 温度序列
            strength: 约束强度 (0-1)
            
        Returns:
            约束后的温度序列
        """
        stats_constraints = self.constraints.get('statistical_constraints', {})
        
        # 检查是否强制精确匹配
        enforce_exact = stats_constraints.get('enforce_exact_match', False)
        
        if not enforce_exact:
            # 软约束模式：只在超出合理范围时调整
            mean_range = stats_constraints.get('mean_range', [129.0, 136.0])
            std_range = stats_constraints.get('std_range', [8.0, 18.0])
            
            current_mean = np.mean(temperature_sequence)
            current_std = np.std(temperature_sequence)
            
            adjusted_sequence = temperature_sequence.copy()
            
            # 只在超出范围时调整均值
            if current_mean < mean_range[0]:
                adjustment = (mean_range[0] - current_mean) * strength
                adjusted_sequence += adjustment
            elif current_mean > mean_range[1]:
                adjustment = (mean_range[1] - current_mean) * strength
                adjusted_sequence += adjustment
            
            # 只在超出范围时调整标准差
            current_std_after_mean = np.std(adjusted_sequence)
            if current_std_after_mean < std_range[0] or current_std_after_mean > std_range[1]:
                if current_std_after_mean > 0:
                    if current_std_after_mean < std_range[0]:
                        target_std = std_range[0]
                    else:
                        target_std = std_range[1]
                    
                    std_ratio = target_std / current_std_after_mean
                    center = np.mean(adjusted_sequence)
                    adjusted_sequence = (adjusted_sequence - center) * std_ratio + center
            
            return adjusted_sequence
        
        else:
            # 硬约束模式：强制匹配目标值
            target_mean = stats_constraints.get('target_mean', 132.6)
            target_std = stats_constraints.get('target_std', 12.3)
            
            current_mean = np.mean(temperature_sequence)
            current_std = np.std(temperature_sequence)
            
            # 强制调整均值
            mean_adjustment = (target_mean - current_mean) * strength
            adjusted_sequence = temperature_sequence + mean_adjustment
            
            # 强制调整标准差
            if current_std > 0:
                std_ratio = target_std / current_std
                adjusted_sequence = (adjusted_sequence - np.mean(adjusted_sequence)) * std_ratio + np.mean(adjusted_sequence)
            
            return adjusted_sequence
    
    def check_gradient_constraints(self, temperature_sequence: np.ndarray) -> Dict[str, Any]:
        """
        检查梯度约束
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            梯度约束检查结果
        """
        gradient_constraints = self.constraints.get('gradient_constraints', {})
        
        # 计算梯度
        gradients = np.diff(temperature_sequence)
        abs_gradients = np.abs(gradients)
        
        # 约束参数
        max_abs_gradient = gradient_constraints.get('max_abs_gradient', 0.3)
        mean_abs_gradient_range = gradient_constraints.get('mean_abs_gradient_range', [0.005, 0.02])
        
        # 检查结果
        max_gradient = np.max(abs_gradients)
        mean_abs_gradient = np.mean(abs_gradients)
        
        results = {
            'max_gradient_valid': max_gradient <= max_abs_gradient,
            'mean_gradient_valid': mean_abs_gradient_range[0] <= mean_abs_gradient <= mean_abs_gradient_range[1],
            'max_gradient': max_gradient,
            'mean_abs_gradient': mean_abs_gradient,
            'violations': np.sum(abs_gradients > max_abs_gradient),
            'violation_ratio': np.sum(abs_gradients > max_abs_gradient) / len(abs_gradients)
        }
        
        return results
    
    def apply_gradient_constraints(self, temperature_sequence: np.ndarray) -> np.ndarray:
        """
        应用梯度约束（基于真实训练数据的极慢上升特征）

        Args:
            temperature_sequence: 温度序列

        Returns:
            约束后的温度序列
        """
        gradient_constraints = self.constraints.get('gradient_constraints', {})
        max_abs_gradient = gradient_constraints.get('max_abs_gradient', 0.124)
        max_rise_rate = gradient_constraints.get('overall_rise_rate_max', 0.001)

        # 逐步修正大梯度，确保极慢上升
        constrained_sequence = temperature_sequence.copy()

        for i in range(1, len(constrained_sequence)):
            gradient = constrained_sequence[i] - constrained_sequence[i-1]

            # 限制单步梯度
            if abs(gradient) > max_abs_gradient:
                sign = np.sign(gradient)
                constrained_sequence[i] = constrained_sequence[i-1] + sign * max_abs_gradient

        # 限制整体上升率
        total_rise = constrained_sequence[-1] - constrained_sequence[0]
        max_total_rise = max_rise_rate * len(constrained_sequence)

        if total_rise > max_total_rise:
            # 重新缩放序列以满足整体上升率约束
            excess_rise = total_rise - max_total_rise
            # 从序列中减去多余的上升，保持起始温度不变
            for i in range(1, len(constrained_sequence)):
                reduction_factor = (i / (len(constrained_sequence) - 1))
                constrained_sequence[i] -= excess_rise * reduction_factor

        return constrained_sequence

    def apply_starting_temperature_constraints(self, temperature_sequence: np.ndarray) -> np.ndarray:
        """
        应用起始温度约束

        Args:
            temperature_sequence: 温度序列

        Returns:
            约束后的温度序列
        """
        starting_constraints = self.constraints.get('starting_temperature_constraints', {})

        # 约束起始温度范围
        min_start = starting_constraints.get('min', 95.0)
        max_start = starting_constraints.get('max', 132.0)
        target_mean = starting_constraints.get('mean', 117.5)

        constrained_sequence = temperature_sequence.copy()

        # 调整起始温度到合理范围
        if constrained_sequence[0] < min_start:
            adjustment = min_start - constrained_sequence[0]
            constrained_sequence += adjustment
        elif constrained_sequence[0] > max_start:
            adjustment = max_start - constrained_sequence[0]
            constrained_sequence += adjustment

        # 调整起始阶段（前5%）的平均温度
        start_length = max(1, len(constrained_sequence) // 20)  # 前5%
        starting_phase = constrained_sequence[:start_length]
        current_start_mean = np.mean(starting_phase)

        start_mean_range = starting_constraints.get('starting_phase_mean_range', [98.0, 132.0])
        if current_start_mean < start_mean_range[0]:
            adjustment = start_mean_range[0] - current_start_mean
            constrained_sequence[:start_length] += adjustment
        elif current_start_mean > start_mean_range[1]:
            adjustment = start_mean_range[1] - current_start_mean
            constrained_sequence[:start_length] += adjustment

        return constrained_sequence

    def apply_phase_constraints(self, temperature_sequence: np.ndarray) -> np.ndarray:
        """
        应用阶段性约束

        Args:
            temperature_sequence: 温度序列

        Returns:
            约束后的温度序列
        """
        phase_constraints = self.constraints.get('phase_constraints', {})

        if not phase_constraints:
            return temperature_sequence

        constrained_sequence = temperature_sequence.copy()
        seq_len = len(constrained_sequence)

        for phase_name, phase_data in phase_constraints.items():
            # 处理新的约束格式
            if 'position' in phase_data:
                start_pct, end_pct = phase_data['position']
            elif 'duration_ratio' in phase_data:
                # 新格式：根据duration_ratio计算位置
                if phase_name == 'early':
                    start_pct, end_pct = 0.0, phase_data['duration_ratio']
                elif phase_name == 'middle':
                    early_ratio = phase_constraints.get('early', {}).get('duration_ratio', 0.3)
                    start_pct = early_ratio
                    end_pct = start_pct + phase_data['duration_ratio']
                elif phase_name == 'late':
                    early_ratio = phase_constraints.get('early', {}).get('duration_ratio', 0.3)
                    middle_ratio = phase_constraints.get('middle', {}).get('duration_ratio', 0.4)
                    start_pct = early_ratio + middle_ratio
                    end_pct = 1.0
                else:
                    continue
            else:
                continue

            start_idx = int(seq_len * start_pct)
            end_idx = int(seq_len * end_pct)

            if end_idx > start_idx:
                phase_sequence = constrained_sequence[start_idx:end_idx]
                temp_range = phase_data['temperature_range']
                target_temp = phase_data.get('target_temperature', np.mean(temp_range))
                max_gradient = phase_data.get('max_gradient', 0.01)

                # 约束阶段温度范围
                phase_mean = np.mean(phase_sequence)
                if phase_mean < temp_range[0]:
                    adjustment = temp_range[0] - phase_mean
                    constrained_sequence[start_idx:end_idx] = constrained_sequence[start_idx:end_idx] + adjustment
                elif phase_mean > temp_range[1]:
                    adjustment = temp_range[1] - phase_mean
                    constrained_sequence[start_idx:end_idx] = constrained_sequence[start_idx:end_idx] + adjustment

                # 约束阶段内的梯度
                if end_idx - start_idx > 1:
                    phase_gradients = np.diff(constrained_sequence[start_idx:end_idx])
                    for i, gradient in enumerate(phase_gradients):
                        if abs(gradient) > max_gradient:
                            sign = np.sign(gradient)
                            constrained_sequence[start_idx + i + 1] = (
                                constrained_sequence[start_idx + i] + sign * max_gradient
                            )

        return constrained_sequence
    
    def check_smoothness_constraints(self, temperature_sequence: np.ndarray) -> Dict[str, Any]:
        """
        检查平滑性约束
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            平滑性约束检查结果
        """
        smoothness_constraints = self.constraints.get('smoothness_constraints', {})
        min_smoothness = smoothness_constraints.get('min_smoothness', 0.8)
        
        # 计算平滑性
        second_diff = np.diff(temperature_sequence, n=2)
        smoothness_score = 1.0 / (1.0 + np.var(second_diff))
        
        results = {
            'smoothness_valid': smoothness_score >= min_smoothness,
            'smoothness_score': smoothness_score,
            'min_smoothness': min_smoothness,
            'second_diff_var': np.var(second_diff)
        }
        
        return results
    
    def apply_smoothness_constraints(self, temperature_sequence: np.ndarray, 
                                   window_size: int = 5) -> np.ndarray:
        """
        应用平滑性约束
        
        Args:
            temperature_sequence: 温度序列
            window_size: 平滑窗口大小
            
        Returns:
            约束后的温度序列
        """
        # 使用移动平均进行平滑
        smoothed_sequence = temperature_sequence.copy()
        
        for i in range(window_size, len(smoothed_sequence) - window_size):
            window = temperature_sequence[i-window_size:i+window_size+1]
            smoothed_sequence[i] = np.mean(window)
        
        return smoothed_sequence
    
    def check_phase_constraints(self, temperature_sequence: np.ndarray) -> Dict[str, Any]:
        """
        检查时间段约束
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            时间段约束检查结果
        """
        phase_constraints = self.constraints.get('phase_constraints', {})
        
        seq_len = len(temperature_sequence)
        
        # 分割为三个阶段
        early_phase = temperature_sequence[:seq_len//4]
        middle_phase = temperature_sequence[seq_len//4:3*seq_len//4]
        late_phase = temperature_sequence[3*seq_len//4:]
        
        results = {}
        
        for phase_name, phase_data in [('early', early_phase), ('middle', middle_phase), ('late', late_phase)]:
            if phase_name in phase_constraints:
                constraints = phase_constraints[phase_name]
                
                phase_mean = np.mean(phase_data)
                phase_std = np.std(phase_data)
                
                mean_range = constraints.get('mean_range', [0, 200])
                std_range = constraints.get('std_range', [0, 50])
                
                results[f'{phase_name}_phase'] = {
                    'mean_valid': mean_range[0] <= phase_mean <= mean_range[1],
                    'std_valid': std_range[0] <= phase_std <= std_range[1],
                    'phase_mean': phase_mean,
                    'phase_std': phase_std,
                    'mean_range': mean_range,
                    'std_range': std_range
                }
        
        return results
    
    def apply_comprehensive_constraints(self, temperature_sequence: np.ndarray,
                                      constraint_strength: float = 0.8) -> np.ndarray:
        """
        应用综合约束（基于真实训练数据的阶段性特征）

        Args:
            temperature_sequence: 温度序列
            constraint_strength: 约束强度 (0-1)

        Returns:
            约束后的温度序列
        """
        constrained_sequence = temperature_sequence.copy()

        # 1. 应用起始温度约束（最重要）
        constrained_sequence = self.apply_starting_temperature_constraints(constrained_sequence)

        # 2. 应用阶段性约束
        constrained_sequence = self.apply_phase_constraints(constrained_sequence)

        # 3. 应用梯度约束（确保极慢上升）
        constrained_sequence = self.apply_gradient_constraints(constrained_sequence)

        # 4. 应用温度边界约束
        constrained_sequence = self.apply_temperature_bounds(constrained_sequence)

        # 5. 应用平滑性约束（确保极高平滑性）
        smoothness_check = self.check_smoothness_constraints(constrained_sequence)
        if not smoothness_check['smoothness_valid']:
            constrained_sequence = self.apply_smoothness_constraints(constrained_sequence, window_size=3)

        # 6. 最终梯度检查（确保没有违反极慢上升约束）
        constrained_sequence = self.apply_gradient_constraints(constrained_sequence)

        # 7. 最终边界检查
        constrained_sequence = self.apply_temperature_bounds(constrained_sequence)

        return constrained_sequence
    
    def validate_sequence(self, temperature_sequence: np.ndarray) -> Dict[str, Any]:
        """
        验证温度序列是否满足所有约束
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            验证结果
        """
        validation_results = {
            'statistical': self.check_statistical_constraints(temperature_sequence),
            'gradient': self.check_gradient_constraints(temperature_sequence),
            'smoothness': self.check_smoothness_constraints(temperature_sequence),
            'phase': self.check_phase_constraints(temperature_sequence)
        }
        
        # 计算总体有效性
        all_valid = True
        for category, results in validation_results.items():
            if category == 'statistical':
                all_valid &= results['mean_valid'] and results['std_valid']
            elif category == 'gradient':
                all_valid &= results['max_gradient_valid'] and results['mean_gradient_valid']
            elif category == 'smoothness':
                all_valid &= results['smoothness_valid']
            elif category == 'phase':
                for phase_result in results.values():
                    all_valid &= phase_result['mean_valid'] and phase_result['std_valid']
        
        validation_results['overall_valid'] = all_valid
        
        return validation_results
