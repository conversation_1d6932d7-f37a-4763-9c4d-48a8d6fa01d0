# Design Document

## Overview

本设计文档描述了如何优化现有的MOEAD多目标优化算法，使其生成的温度序列解严格遵循21个真实数据样本的温度变化走势。设计的核心思想是将真实样本数据的统计特征、变化模式和阶段特征深度集成到优化算法的各个组件中，确保生成的解在保持优化性能的同时，与真实数据保持高度一致性。

## Architecture

### 系统架构概览

```mermaid
graph TB
    A[Sample Data Analyzer] --> B[Sample-Based Population Initializer]
    A --> C[Sample-Driven Objective Functions]
    A --> D[Sample-Aware Genetic Operators]
    
    B --> E[MOEAD Core Engine]
    C --> E
    D --> E
    
    E --> F[Sample Constraint Validator]
    F --> G[Optimized Solutions]
    
    H[Real Sample Data] --> A
    I[Trained Classifier] --> C
    
    subgraph "Sample Analysis Layer"
        A
        J[Statistical Feature Extractor]
        K[Pattern Matcher]
        L[Stage Analyzer]
    end
    
    subgraph "Optimization Layer"
        E
        M[Population Manager]
        N[Selection Operator]
        O[Crossover Operator]
        P[Mutation Operator]
    end
    
    subgraph "Validation Layer"
        F
        Q[Statistical Validator]
        R[Pattern Validator]
        S[Stage Validator]
    end
```

### 核心组件关系

1. **Sample Data Analyzer**: 分析21个样本数据，提取统计特征和变化模式
2. **Sample-Based Population Initializer**: 基于样本特征生成初始种群
3. **Sample-Driven Objective Functions**: 使用样本相似度作为优化目标
4. **Sample-Aware Genetic Operators**: 在遗传操作中保持样本特征
5. **Sample Constraint Validator**: 验证生成解的样本一致性

## Components and Interfaces

### 1. SampleDataAnalyzer

**职责**: 分析真实样本数据，提取关键特征和模式

**接口**:
```python
class SampleDataAnalyzer:
    def load_sample_data() -> Dict[str, np.ndarray]
    def calculate_sample_statistics() -> Dict[str, Dict]
    def get_reference_template() -> np.ndarray
    def calculate_sequence_similarity(sequence: np.ndarray) -> Dict[str, float]
    def _calculate_stage_changes(sequence: np.ndarray) -> List[float]
```

**关键功能**:
- 加载21个Sample_*.xlsx文件
- 计算统计特征（均值、标准差、峰值等）
- 分析五阶段变化模式
- 提供相似度计算方法

### 2. SampleBasedSequenceGenerator

**职责**: 基于样本数据生成符合真实特征的温度序列

**接口**:
```python
class SampleBasedSequenceGenerator:
    def generate_sample_based_sequence(seed: Optional[int]) -> np.ndarray
    def generate_population(population_size: int) -> List[np.ndarray]
    def _generate_five_stage_sequence(...) -> np.ndarray
    def _apply_sample_constraints(sequence: np.ndarray) -> np.ndarray
```

**关键功能**:
- 基于Sample_1作为参考模板
- 生成符合五阶段模式的序列
- 应用样本统计约束
- 确保生成序列的多样性

### 3. SampleDataDrivenObjectiveFunctions

**职责**: 实现基于样本数据的多目标函数

**接口**:
```python
class SampleDataDrivenObjectiveFunctions:
    def objective_1_statistical_deviation(sequence: np.ndarray) -> float
    def objective_2_pattern_matching(sequence: np.ndarray) -> float
    def objective_3_stage_pattern_compliance(sequence: np.ndarray) -> float
    def evaluate(sequence: np.ndarray) -> Dict[str, float]
```

**目标函数设计**:
- **目标1**: 最小化与样本统计特征的偏差
- **目标2**: 最小化与Sample_1模式的差异
- **目标3**: 最小化五阶段模式违反程度

### 4. Enhanced MOEAD Optimizer

**职责**: 集成样本驱动组件的MOEAD优化器

**接口**:
```python
class EnhancedMOEADOptimizer(MOEADOptimizer):
    def initialize_sample_based_population() -> None
    def sample_aware_crossover(...) -> Individual
    def sample_aware_mutation(...) -> Individual
    def validate_sample_compliance(...) -> bool
```

**增强功能**:
- 样本驱动的种群初始化
- 保持样本特征的遗传操作
- 样本一致性验证机制

## Data Models

### 样本统计特征模型

```python
@dataclass
class SampleStatistics:
    # 基础统计
    initial_temp: float  # 起始温度
    final_temp: float    # 最终温度
    peak_temp: float     # 峰值温度
    peak_position: float # 峰值位置比例
    temp_range: float    # 温度范围
    total_change: float  # 总变化量
    
    # 阶段变化
    stage_changes: List[float]  # 五阶段变化量
    
    # 变化率特征
    max_increase: float     # 最大升温率
    max_decrease: float     # 最大降温率
    mean_change_rate: float # 平均变化率
```

### 整体统计特征模型

```python
@dataclass
class OverallStatistics:
    initial_temp: StatRange    # 起始温度统计
    final_temp: StatRange      # 最终温度统计
    peak_temp: StatRange       # 峰值温度统计
    peak_position: StatRange   # 峰值位置统计
    total_change: StatRange    # 总变化量统计

@dataclass
class StatRange:
    mean: float
    std: float
    min: float
    max: float
```

### 相似度评估模型

```python
@dataclass
class SimilarityMetrics:
    correlation: float              # 皮尔逊相关系数
    euclidean_similarity: float     # 欧几里得相似度
    initial_temp_similarity: float  # 起始温度相似度
    final_temp_similarity: float    # 最终温度相似度
    peak_temp_similarity: float     # 峰值温度相似度
    peak_position_similarity: float # 峰值位置相似度
    overall_similarity: float       # 综合相似度
```

## Error Handling

### 1. 数据加载错误处理

```python
def load_sample_data_with_fallback():
    try:
        # 尝试加载所有样本文件
        sample_data = load_all_samples()
        if len(sample_data) < 21:
            logger.warning(f"只加载了{len(sample_data)}个样本，少于预期的21个")
        return sample_data
    except FileNotFoundError as e:
        logger.error(f"样本文件未找到: {e}")
        # 使用默认样本或生成模拟数据
        return generate_fallback_samples()
    except Exception as e:
        logger.error(f"加载样本数据失败: {e}")
        raise OptimizationError("无法加载必要的样本数据")
```

### 2. 约束违反处理

```python
def handle_constraint_violations(sequence: np.ndarray) -> np.ndarray:
    """处理约束违反，确保序列符合样本特征"""
    try:
        # 检查约束违反
        violations = check_sample_constraints(sequence)
        
        if violations['total_violations'] > 0:
            # 应用修正策略
            corrected_sequence = apply_constraint_corrections(sequence, violations)
            
            # 验证修正结果
            if validate_corrected_sequence(corrected_sequence):
                return corrected_sequence
            else:
                # 如果修正失败，重新生成
                return regenerate_compliant_sequence()
        
        return sequence
        
    except Exception as e:
        logger.error(f"约束处理失败: {e}")
        # 返回最接近的有效序列
        return get_nearest_valid_sequence(sequence)
```

### 3. 优化过程错误处理

```python
def robust_optimization_step():
    """鲁棒的优化步骤，包含错误恢复机制"""
    try:
        # 执行正常优化步骤
        return execute_normal_optimization_step()
        
    except ConvergenceError:
        logger.warning("优化收敛困难，调整参数")
        return adjust_parameters_and_retry()
        
    except ConstraintError:
        logger.warning("约束违反过多，重新初始化部分种群")
        return reinitialize_partial_population()
        
    except Exception as e:
        logger.error(f"优化步骤失败: {e}")
        return emergency_recovery_procedure()
```

## Testing Strategy

### 1. 单元测试

**SampleDataAnalyzer测试**:
```python
def test_sample_data_loading():
    """测试样本数据加载功能"""
    analyzer = SampleDataAnalyzer()
    sample_data = analyzer.load_sample_data()
    
    assert len(sample_data) == 21
    assert 'Sample_1' in sample_data
    assert all(len(seq) > 0 for seq in sample_data.values())

def test_statistical_calculation():
    """测试统计特征计算"""
    analyzer = SampleDataAnalyzer()
    stats = analyzer.calculate_sample_statistics()
    
    # 验证统计特征的合理性
    assert 13.0 <= stats['Sample_1']['initial_temp'] <= 103.0
    assert 129.0 <= stats['Sample_1']['final_temp'] <= 147.0
    assert len(stats['Sample_1']['stage_changes']) == 5
```

**目标函数测试**:
```python
def test_objective_functions():
    """测试样本驱动的目标函数"""
    analyzer = SampleDataAnalyzer()
    analyzer.load_sample_data()
    analyzer.calculate_sample_statistics()
    
    obj_func = SampleDataDrivenObjectiveFunctions(analyzer)
    
    # 使用Sample_1作为测试序列
    test_sequence = analyzer.get_reference_template()
    objectives = obj_func.evaluate(test_sequence)
    
    # Sample_1应该与自己高度相似
    assert objectives['f2_pattern_difference'] < 0.1
    assert objectives['f1_statistical_deviation'] < 0.1
```

### 2. 集成测试

**端到端优化测试**:
```python
def test_end_to_end_optimization():
    """测试完整的样本驱动优化流程"""
    # 初始化优化器
    optimizer = create_sample_driven_optimizer()
    
    # 运行优化
    results = optimizer.optimize(max_generations=50)
    
    # 验证结果质量
    best_solutions = results['pareto_front']
    
    for solution in best_solutions:
        # 检查样本一致性
        similarity = calculate_sample_similarity(solution)
        assert similarity['overall_similarity'] > 0.8
        
        # 检查五阶段模式
        stage_compliance = check_stage_pattern_compliance(solution)
        assert stage_compliance['total_violation'] < 0.2
```

### 3. 性能测试

**优化效率测试**:
```python
def test_optimization_efficiency():
    """测试优化算法的效率和收敛性"""
    optimizer = create_sample_driven_optimizer()
    
    start_time = time.time()
    results = optimizer.optimize(max_generations=100)
    end_time = time.time()
    
    # 检查运行时间
    assert end_time - start_time < 3600  # 1小时内完成
    
    # 检查收敛性
    convergence_history = results['convergence_history']
    assert len(convergence_history) > 0
    assert convergence_history[-1]['hypervolume'] > convergence_history[0]['hypervolume']
```

### 4. 样本一致性测试

**统计特征一致性测试**:
```python
def test_statistical_consistency():
    """测试生成解的统计特征一致性"""
    optimizer = create_sample_driven_optimizer()
    solutions = optimizer.generate_initial_population()
    
    for solution in solutions:
        stats = calculate_sequence_statistics(solution)
        
        # 检查起始温度分布
        assert 13.5 <= stats['initial_temp'] <= 102.1
        
        # 检查最终温度分布
        assert 129.0 <= stats['final_temp'] <= 146.6
        
        # 检查峰值温度
        assert 143.9 <= stats['peak_temp'] <= 151.3
```

**模式匹配测试**:
```python
def test_pattern_matching():
    """测试生成解的模式匹配能力"""
    analyzer = SampleDataAnalyzer()
    analyzer.load_sample_data()
    
    optimizer = create_sample_driven_optimizer()
    solutions = optimizer.optimize(max_generations=20)['pareto_front']
    
    for solution in solutions:
        similarity = analyzer.calculate_sequence_similarity(solution)
        
        # 至少70%的解应该与参考模板相似度超过0.8
        high_similarity_count = sum(1 for s in solutions 
                                  if analyzer.calculate_sequence_similarity(s)['overall_similarity'] > 0.8)
        
        assert high_similarity_count / len(solutions) >= 0.7
```

## Implementation Considerations

### 1. 性能优化

- **并行计算**: 目标函数评估和相似度计算可以并行化
- **缓存机制**: 缓存样本统计特征和相似度计算结果
- **内存管理**: 优化大规模温度序列的内存使用

### 2. 参数调优

- **权重平衡**: 三个目标函数的权重需要仔细调优
- **约束强度**: 样本约束的强度需要平衡优化性能和一致性
- **种群多样性**: 确保在保持样本特征的同时维持种群多样性

### 3. 扩展性设计

- **模块化架构**: 各组件独立，便于替换和扩展
- **配置驱动**: 通过配置文件调整算法参数
- **接口标准化**: 标准化的接口便于集成其他优化算法

### 4. 鲁棒性保证

- **异常处理**: 完善的异常处理机制
- **参数验证**: 输入参数的有效性验证
- **降级策略**: 在约束无法满足时的降级处理策略