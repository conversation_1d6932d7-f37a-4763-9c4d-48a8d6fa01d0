#!/usr/bin/env python3
"""
粒子群优化算法模块

该模块负责：
1. 实现PSO算法核心逻辑
2. 温度序列的粒子表示和约束处理
3. 自适应参数调整
4. 收敛性分析
"""

import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Tuple, Callable, Optional
import logging
import yaml
from datetime import datetime
import copy

logger = logging.getLogger(__name__)


class Particle:
    """PSO粒子类 (支持变长序列优化)"""

    def __init__(self, dimension: int, bounds: Tuple[float, float],
                 variable_length_enabled: bool = False):
        """
        初始化粒子

        Args:
            dimension: 粒子维度（控制点数量，变长模式下+1用于长度参数）
            bounds: 温度边界 (min_temp, max_temp)
            variable_length_enabled: 是否启用变长序列优化
        """
        self.dimension = dimension
        self.bounds = bounds
        self.variable_length_enabled = variable_length_enabled

        if variable_length_enabled:
            # 变长模式：第一维是长度参数[0,1]，其余是温度控制点
            self.position = np.zeros(dimension)
            self.position[0] = np.random.uniform(0, 1)  # 长度参数
            self.position[1:] = np.random.uniform(bounds[0], bounds[1], dimension-1)  # 控制点

            # 速度初始化
            self.velocity = np.zeros(dimension)
            self.velocity[0] = np.random.uniform(-0.1, 0.1)  # 长度参数速度较小
            self.velocity[1:] = np.random.uniform(-1, 1, dimension-1)  # 控制点速度
        else:
            # 固定长度模式：所有维度都是温度控制点
            self.position = np.random.uniform(bounds[0], bounds[1], dimension)
            self.velocity = np.random.uniform(-1, 1, dimension)

        # 个体最优
        self.best_position = self.position.copy()
        self.best_fitness = float('-inf')

        # 当前适应度
        self.fitness = float('-inf')
        
    def update_velocity(self, global_best_position: np.ndarray,
                       w: float, c1: float, c2: float):
        """
        更新粒子速度 (支持变长序列优化)

        Args:
            global_best_position: 全局最优位置
            w: 惯性权重
            c1: 个体学习因子
            c2: 社会学习因子
        """
        r1 = np.random.random(self.dimension)
        r2 = np.random.random(self.dimension)

        # PSO速度更新公式
        cognitive_component = c1 * r1 * (self.best_position - self.position)
        social_component = c2 * r2 * (global_best_position - self.position)

        self.velocity = w * self.velocity + cognitive_component + social_component

        if self.variable_length_enabled:
            # 变长模式：不同维度使用不同的速度限制
            # 长度参数速度限制较小
            length_max_velocity = 0.05  # 长度参数变化较慢
            temp_max_velocity = (self.bounds[1] - self.bounds[0]) * 0.1  # 温度控制点

            self.velocity[0] = np.clip(self.velocity[0], -length_max_velocity, length_max_velocity)
            self.velocity[1:] = np.clip(self.velocity[1:], -temp_max_velocity, temp_max_velocity)
        else:
            # 固定长度模式：统一速度限制
            max_velocity = (self.bounds[1] - self.bounds[0]) * 0.1
            self.velocity = np.clip(self.velocity, -max_velocity, max_velocity)
    
    def update_position(self):
        """更新粒子位置 (支持变长序列优化)"""
        self.position += self.velocity

        if self.variable_length_enabled:
            # 变长模式：不同维度使用不同的边界约束
            self.position[0] = np.clip(self.position[0], 0, 1)  # 长度参数[0,1]
            self.position[1:] = np.clip(self.position[1:], self.bounds[0], self.bounds[1])  # 温度控制点
        else:
            # 固定长度模式：统一边界约束
            self.position = np.clip(self.position, self.bounds[0], self.bounds[1])
    
    def update_best(self):
        """更新个体最优"""
        if self.fitness > self.best_fitness:
            self.best_fitness = self.fitness
            self.best_position = self.position.copy()


class PSOOptimizer:
    """粒子群优化器"""
    
    def __init__(self, config_or_path = "config/config.yaml"):
        """
        初始化PSO优化器

        Args:
            config_or_path: 配置文件路径(str)或配置对象(dict)
        """
        if isinstance(config_or_path, str):
            # 传入的是配置文件路径
            with open(config_or_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
        elif isinstance(config_or_path, dict):
            # 传入的是配置对象
            self.config = config_or_path
        else:
            raise ValueError("config_or_path必须是字符串(文件路径)或字典(配置对象)")
        
        self.pso_config = self.config['pso']
        self.temp_config = self.pso_config['temperature_sequence']
        
        # PSO参数
        self.swarm_size = self.pso_config['swarm_size']
        self.max_iterations = self.pso_config['max_iterations']
        self.w = self.pso_config['w']
        self.c1 = self.pso_config['c1']
        self.c2 = self.pso_config['c2']
        
        # 自适应参数
        self.adaptive_config = self.pso_config['adaptive']
        self.w_min = self.adaptive_config['w_min']
        self.w_max = self.adaptive_config['w_max']
        
        # 收敛参数
        self.convergence_config = self.pso_config['convergence']
        self.tolerance = float(self.convergence_config['tolerance'])  # 确保是浮点数
        self.patience = int(self.convergence_config['patience'])  # 确保是整数
        
        # 温度序列参数
        self.control_points = self.temp_config['control_points']
        self.min_temp = self.temp_config['min_temperature']
        self.max_temp = self.temp_config['max_temperature']
        self.max_change_rate = self.temp_config['max_change_rate']
        self.sequence_length = self.temp_config['sequence_length']

        # 变长序列参数
        self.variable_length_config = self.temp_config.get('variable_length', {})
        self.variable_length_enabled = self.variable_length_config.get('enable', False)

        if self.variable_length_enabled:
            self.min_length = self.variable_length_config['min_length']
            self.max_length = self.variable_length_config['max_length']
            self.default_length = self.variable_length_config['default_length']
            self.length_weight = self.variable_length_config['length_weight']
            self.length_penalty_factor = self.variable_length_config['length_penalty_factor']
            self.interpolation_method = self.variable_length_config['interpolation_method']

            logger.info(f"启用变长序列优化: 长度范围[{self.min_length}, {self.max_length}]")
        else:
            logger.info(f"使用固定序列长度: {self.sequence_length}")
        
        # 粒子群
        self.swarm = []
        self.global_best_position = None
        self.global_best_fitness = float('-inf')
        
        # 优化历史
        self.fitness_history = []
        self.best_fitness_history = []
        self.diversity_history = []

        # 变长序列优化历史
        if self.variable_length_enabled:
            self.length_history = []  # 最优序列长度历史
            self.length_diversity_history = []  # 长度多样性历史

        # 固定前缀配置
        self.fixed_prefix_enabled = False
        self.fixed_prefix_data = None
        self.fixed_prefix_metadata = None
        self.fixed_prefix_length = 0

    def set_fixed_prefix(self, fixed_prefix_data: np.ndarray, metadata: Dict = None):
        """
        设置固定前缀序列

        Args:
            fixed_prefix_data: 固定前缀温度序列
            metadata: 固定前缀元数据
        """
        self.fixed_prefix_enabled = True
        self.fixed_prefix_data = fixed_prefix_data.copy()
        self.fixed_prefix_metadata = metadata or {}
        self.fixed_prefix_length = len(fixed_prefix_data)

        logger.info(f"已设置固定前缀: {self.fixed_prefix_length:,} 个数据点")
        logger.info(f"固定前缀温度范围: [{np.min(fixed_prefix_data):.2f}, {np.max(fixed_prefix_data):.2f}]°C")

    def initialize_swarm(self):
        """初始化粒子群 (支持变长序列优化)"""
        self.swarm = []
        bounds = (self.min_temp, self.max_temp)

        # 确定粒子维度
        if self.variable_length_enabled:
            particle_dimension = self.control_points + 1  # +1 for length parameter
        else:
            particle_dimension = self.control_points

        for _ in range(self.swarm_size):
            particle = Particle(particle_dimension, bounds, self.variable_length_enabled)

            if self.variable_length_enabled:
                # 变长模式：只对温度控制点应用变化率约束
                temp_control_points = particle.position[1:].copy()
                temp_control_points = self._apply_change_rate_constraint(temp_control_points)
                particle.position[1:] = temp_control_points
            else:
                # 固定长度模式：对所有控制点应用约束
                particle.position = self._apply_change_rate_constraint(particle.position)

            self.swarm.append(particle)

        if self.variable_length_enabled:
            logger.info(f"初始化了 {self.swarm_size} 个变长粒子，维度: {particle_dimension} (1个长度参数 + {self.control_points}个控制点)")
        else:
            logger.info(f"初始化了 {self.swarm_size} 个固定长度粒子，每个粒子 {self.control_points} 个控制点")

        if self.fixed_prefix_enabled:
            logger.info(f"启用固定前缀模式: 前 {self.fixed_prefix_length:,} 个数据点将保持固定")
    
    def _apply_change_rate_constraint(self, control_points: np.ndarray) -> np.ndarray:
        """
        应用温度变化率约束
        
        Args:
            control_points: 控制点数组
            
        Returns:
            约束后的控制点数组
        """
        constrained_points = control_points.copy()
        
        for i in range(1, len(constrained_points)):
            max_change = self.max_change_rate * (self.sequence_length / self.control_points)
            
            # 限制相邻控制点之间的最大变化
            if constrained_points[i] - constrained_points[i-1] > max_change:
                constrained_points[i] = constrained_points[i-1] + max_change
            elif constrained_points[i-1] - constrained_points[i] > max_change:
                constrained_points[i] = constrained_points[i-1] - max_change
        
        return constrained_points

    def _apply_sequence_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用序列级别的约束，包括业务趋势约束

        Args:
            sequence: 原始温度序列

        Returns:
            约束后的温度序列
        """
        constrained_sequence = sequence.copy()

        # 1. 温度范围约束
        constrained_sequence = np.clip(constrained_sequence, self.min_temp, self.max_temp)

        # 2. 业务趋势约束：确保整体上升趋势
        constrained_sequence = self._apply_business_trend_constraint(constrained_sequence)

        # 3. 变化率约束 (包含起始阶段稳定性)
        initial_points = 50  # 起始稳定点数量
        initial_max_change = 0.1  # 起始阶段最大变化率

        for i in range(1, len(constrained_sequence)):
            change = constrained_sequence[i] - constrained_sequence[i-1]

            # 起始阶段使用更严格的变化率约束
            if i <= initial_points:
                max_allowed_change = initial_max_change
            else:
                max_allowed_change = self.max_change_rate

            if abs(change) > max_allowed_change:
                sign = 1 if change > 0 else -1
                constrained_sequence[i] = constrained_sequence[i-1] + sign * max_allowed_change

        return constrained_sequence

    def _apply_business_trend_constraint(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用业务趋势约束：确保温度序列整体呈上升趋势

        基于数据集分析：100%的样本都是上升趋势，平均温度变化+109.39°C

        Args:
            sequence: 原始序列

        Returns:
            符合业务趋势的序列
        """
        if len(sequence) < 2:
            return sequence

        constrained_sequence = sequence.copy()

        # 计算期望的总体上升幅度 (基于数据集统计)
        start_temp = constrained_sequence[0]

        # 确保起始温度在合理范围内 (基于样本数据分析，进一步降低: 16.0-32.0°C)
        start_temp_min, start_temp_max = 16.0, 32.0
        if start_temp > start_temp_max or start_temp < start_temp_min:
            start_temp = np.random.uniform(start_temp_min, start_temp_max)
            constrained_sequence[0] = start_temp

        # 计算目标结束温度 (基于样本数据分析: +88~130°C上升)
        target_temp_rise = np.random.uniform(88, 130)

        # 确保结束温度在合理范围内 (基于样本数据分析: 136.1-145.4°C)
        end_temp_min, end_temp_max = 136.1, 145.4
        target_end_temp = np.clip(start_temp + target_temp_rise, end_temp_min, min(end_temp_max, self.max_temp))

        # 使用单调递增插值确保整体上升趋势
        sequence_length = len(constrained_sequence)

        # 生成单调递增的基础趋势
        base_trend = np.linspace(start_temp, target_end_temp, sequence_length)

        # 保留原序列的局部变化特征，但确保整体趋势
        # 计算原序列相对于线性趋势的偏差
        original_trend = np.linspace(constrained_sequence[0], constrained_sequence[-1], sequence_length)
        deviations = constrained_sequence - original_trend

        # 限制偏差幅度，避免破坏整体上升趋势
        max_deviation = (target_end_temp - start_temp) * 0.1  # 允许10%的偏差
        deviations = np.clip(deviations, -max_deviation, max_deviation)

        # 应用约束后的序列
        constrained_sequence = base_trend + deviations

        # 最终确保单调性（允许小幅波动但整体上升）
        for i in range(1, len(constrained_sequence)):
            # 确保每个点不低于前一个点太多
            min_allowed = constrained_sequence[i-1] - 2.0  # 允许小幅下降
            constrained_sequence[i] = max(constrained_sequence[i], min_allowed)

        return constrained_sequence

    def control_points_to_sequence(self, particle_position: np.ndarray) -> np.ndarray:
        """
        将粒子位置转换为完整的温度序列 (支持变长序列优化)

        Args:
            particle_position: 粒子位置数组 (变长模式: [length_param, control_points...])

        Returns:
            完整的温度序列
        """
        if self.variable_length_enabled:
            # 变长模式：第一维是长度参数，其余是控制点
            length_param = particle_position[0]
            control_points = particle_position[1:]

            # 计算实际序列长度
            actual_length = int(self.min_length + length_param * (self.max_length - self.min_length))
            actual_length = max(self.min_length, min(actual_length, self.max_length))

            # 生成变长序列
            x_control = np.linspace(0, actual_length - 1, len(control_points))
            x_sequence = np.arange(actual_length)

            # 根据插值方法生成序列
            if self.interpolation_method == "cubic_spline":
                try:
                    from scipy import interpolate
                    tck = interpolate.splrep(x_control, control_points, s=0)
                    temperature_sequence = interpolate.splev(x_sequence, tck)
                except Exception as e:
                    logger.warning(f"三次样条插值失败，使用线性插值: {e}")
                    temperature_sequence = np.interp(x_sequence, x_control, control_points)
            else:
                # 线性插值
                temperature_sequence = np.interp(x_sequence, x_control, control_points)
        else:
            # 固定长度模式：所有维度都是控制点
            control_points = particle_position
            x_control = np.linspace(0, self.sequence_length - 1, self.control_points)
            x_sequence = np.arange(self.sequence_length)
            temperature_sequence = np.interp(x_sequence, x_control, control_points)

        # 应用业务趋势约束和其他约束
        temperature_sequence = self._apply_sequence_constraints(temperature_sequence)

        # 如果启用固定前缀，合并固定前缀和优化序列
        if self.fixed_prefix_enabled:
            temperature_sequence = self._merge_fixed_prefix_and_optimized_sequence(temperature_sequence)

        return temperature_sequence
    
    def calculate_swarm_diversity(self) -> float:
        """
        计算粒子群多样性
        
        Returns:
            多样性指标
        """
        positions = np.array([particle.position for particle in self.swarm])
        
        # 计算所有粒子位置的标准差
        diversity = np.mean(np.std(positions, axis=0))
        
        return diversity
    
    def update_inertia_weight(self, iteration: int) -> float:
        """
        自适应更新惯性权重
        
        Args:
            iteration: 当前迭代次数
            
        Returns:
            更新后的惯性权重
        """
        if self.adaptive_config['enable']:
            # 线性递减策略
            w = self.w_max - (self.w_max - self.w_min) * iteration / self.max_iterations
        else:
            w = self.w
        
        return w
    
    def optimize(self, fitness_function: Callable[[np.ndarray], float]) -> Dict:
        """
        执行PSO优化
        
        Args:
            fitness_function: 适应度评估函数
            
        Returns:
            优化结果字典
        """
        logger.info("开始PSO优化...")
        print(f"\n[PSO优化] 开始优化过程...")
        print(f"[PSO优化] 粒子群大小: {self.swarm_size}, 最大迭代次数: {self.max_iterations}")
        print(f"[PSO优化] 控制点数量: {self.control_points}")
        if self.variable_length_enabled:
            print(f"[PSO优化] 变长序列模式: 长度范围 [{self.min_length:,}, {self.max_length:,}]")
        print("-" * 80)

        # 初始化粒子群
        self.initialize_swarm()
        
        # 重置历史记录
        self.fitness_history = []
        self.best_fitness_history = []
        self.diversity_history = []
        
        # 评估初始适应度
        for particle in self.swarm:
            sequence = self.control_points_to_sequence(particle.position)
            particle.fitness = fitness_function(sequence)
            particle.update_best()

            # 更新全局最优
            if particle.fitness > self.global_best_fitness:
                self.global_best_fitness = particle.fitness
                self.global_best_position = particle.position.copy()
        
        # 记录初始状态
        current_fitnesses = [p.fitness for p in self.swarm]
        self.fitness_history.append(current_fitnesses)
        self.best_fitness_history.append(self.global_best_fitness)
        self.diversity_history.append(self.calculate_swarm_diversity())
        
        # 收敛检测
        no_improvement_count = 0
        last_best_fitness = self.global_best_fitness
        
        # 主优化循环
        for iteration in range(self.max_iterations):
            # 更新惯性权重
            current_w = self.update_inertia_weight(iteration)
            
            # 更新每个粒子
            for particle in self.swarm:
                # 更新速度和位置
                particle.update_velocity(self.global_best_position, current_w, self.c1, self.c2)
                particle.update_position()
                
                # 应用约束
                if self.variable_length_enabled:
                    # 变长模式：只对温度控制点应用变化率约束
                    temp_control_points = particle.position[1:].copy()
                    temp_control_points = self._apply_change_rate_constraint(temp_control_points)
                    particle.position[1:] = temp_control_points
                else:
                    # 固定长度模式：对所有控制点应用约束
                    particle.position = self._apply_change_rate_constraint(particle.position)

                # 评估适应度
                sequence = self.control_points_to_sequence(particle.position)
                particle.fitness = fitness_function(sequence)
                
                # 更新个体最优
                particle.update_best()
                
                # 更新全局最优
                if particle.fitness > self.global_best_fitness:
                    self.global_best_fitness = particle.fitness
                    self.global_best_position = particle.position.copy()
            
            # 记录历史
            current_fitnesses = [p.fitness for p in self.swarm]
            self.fitness_history.append(current_fitnesses)
            self.best_fitness_history.append(self.global_best_fitness)
            self.diversity_history.append(self.calculate_swarm_diversity())

            # 记录变长序列历史
            if self.variable_length_enabled:
                # 记录最优序列长度
                best_length_param = self.global_best_position[0]
                best_length = int(self.min_length + best_length_param * (self.max_length - self.min_length))
                self.length_history.append(best_length)

                # 记录长度多样性
                length_params = [particle.position[0] for particle in self.swarm]
                length_diversity = np.std(length_params)
                self.length_diversity_history.append(length_diversity)
            
            # 收敛检测 - 确保数据类型正确
            try:
                # 确保适应度值是数值类型
                current_fitness = float(self.global_best_fitness)
                last_fitness = float(last_best_fitness)

                if abs(current_fitness - last_fitness) < self.tolerance:
                    no_improvement_count += 1
                else:
                    no_improvement_count = 0

                last_best_fitness = current_fitness

            except (TypeError, ValueError) as e:
                logger.warning(f"适应度值类型错误: {e}, 当前值: {self.global_best_fitness}, 上次值: {last_best_fitness}")
                # 如果类型转换失败，跳过收敛检测
                no_improvement_count = 0
                last_best_fitness = self.global_best_fitness
            
            # 打印进度 - 更频繁的显示
            if iteration % 5 == 0 or iteration < 10:
                progress_percent = (iteration / self.max_iterations) * 100
                print(f"[PSO优化] 迭代 {iteration:3d}/{self.max_iterations} ({progress_percent:5.1f}%) | "
                      f"最佳适应度: {self.global_best_fitness:.6f} | "
                      f"多样性: {self.diversity_history[-1]:.6f}")

                # 同时记录到日志
                logger.info(f"迭代 {iteration}: 最佳适应度 = {self.global_best_fitness:.6f}, "
                           f"多样性 = {self.diversity_history[-1]:.6f}")
            
            # 早停检测
            if no_improvement_count >= self.patience:
                logger.info(f"在第 {iteration} 代达到收敛条件，提前停止")
                break
        
        # 生成最优温度序列
        best_sequence = self.control_points_to_sequence(self.global_best_position)
        
        # 优化结果
        optimization_results = {
            'best_control_points': self.global_best_position.tolist(),
            'best_temperature_sequence': best_sequence.tolist(),
            'best_fitness': self.global_best_fitness,
            'total_iterations': iteration + 1,
            'converged': no_improvement_count >= self.patience,
            'fitness_history': self.fitness_history,
            'best_fitness_history': self.best_fitness_history,
            'diversity_history': self.diversity_history,
            'final_diversity': self.diversity_history[-1],
            'optimization_time': datetime.now().isoformat(),
            'variable_length_enabled': self.variable_length_enabled
        }

        # 添加变长序列特有的结果
        if self.variable_length_enabled:
            best_length_param = self.global_best_position[0]
            best_length = int(self.min_length + best_length_param * (self.max_length - self.min_length))

            optimization_results.update({
                'best_sequence_length': best_length,
                'best_length_param': best_length_param,
                'length_history': self.length_history,
                'length_diversity_history': self.length_diversity_history,
                'length_range': [self.min_length, self.max_length]
            })

        # 添加固定前缀相关结果
        if self.fixed_prefix_enabled:
            optimization_results.update({
                'fixed_prefix_info': self.get_fixed_prefix_info(),
                'total_sequence_length': len(best_sequence),
                'optimized_portion_length': len(best_sequence) - self.fixed_prefix_length
            })
        
        print("-" * 80)
        print(f"[PSO优化] 优化完成！")
        print(f"[PSO优化] 最佳适应度: {self.global_best_fitness:.6f}")
        print(f"[PSO优化] 总迭代次数: {iteration + 1}")
        print(f"[PSO优化] 是否收敛: {'是' if optimization_results['converged'] else '否'}")

        if self.variable_length_enabled:
            print(f"[PSO优化] 最优序列长度: {optimization_results['best_sequence_length']:,} 个数据点")
            print(f"[PSO优化] 长度参数: {optimization_results['best_length_param']:.4f}")

        if self.fixed_prefix_enabled:
            print(f"[PSO优化] 固定前缀长度: {self.fixed_prefix_length:,} 个数据点")
            print(f"[PSO优化] 优化部分长度: {optimization_results['optimized_portion_length']:,} 个数据点")
            print(f"[PSO优化] 总序列长度: {optimization_results['total_sequence_length']:,} 个数据点")

        print(f"[PSO优化] 最优温度序列已生成")
        print("-" * 80)

        logger.info(f"PSO优化完成！")
        logger.info(f"最佳适应度: {self.global_best_fitness:.6f}")
        logger.info(f"总迭代次数: {iteration + 1}")
        logger.info(f"是否收敛: {optimization_results['converged']}")

        if self.variable_length_enabled:
            logger.info(f"最优序列长度: {optimization_results['best_sequence_length']:,} 个数据点")
            logger.info(f"长度参数: {optimization_results['best_length_param']:.4f}")
            logger.info(f"长度范围: [{self.min_length:,}, {self.max_length:,}]")

        if self.fixed_prefix_enabled:
            logger.info(f"固定前缀长度: {self.fixed_prefix_length:,} 个数据点")
            logger.info(f"优化部分长度: {optimization_results['optimized_portion_length']:,} 个数据点")
            logger.info(f"总序列长度: {optimization_results['total_sequence_length']:,} 个数据点")
        
        return optimization_results

    def plot_convergence(self, save_path: str = None):
        """
        绘制收敛曲线

        Args:
            save_path: 保存路径
        """
        if not self.best_fitness_history:
            raise ValueError("没有优化历史数据")

        plt.figure(figsize=(12, 8))

        # 最佳适应度收敛曲线
        plt.subplot(2, 2, 1)
        plt.plot(self.best_fitness_history, 'b-', linewidth=2, label='最佳适应度')
        plt.xlabel('迭代次数')
        plt.ylabel('适应度')
        plt.title('适应度收敛曲线')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 粒子群多样性
        plt.subplot(2, 2, 2)
        plt.plot(self.diversity_history, 'g-', linewidth=2, label='多样性')
        plt.xlabel('迭代次数')
        plt.ylabel('多样性')
        plt.title('粒子群多样性变化')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 适应度分布箱线图
        plt.subplot(2, 2, 3)
        if len(self.fitness_history) > 10:
            # 选择几个关键迭代的适应度分布
            iterations_to_plot = [0, len(self.fitness_history)//4, len(self.fitness_history)//2,
                                len(self.fitness_history)*3//4, -1]
            fitness_data = [self.fitness_history[i] for i in iterations_to_plot]
            labels = [f'第{i+1}代' if i >= 0 else '最后一代' for i in iterations_to_plot]

            plt.boxplot(fitness_data, labels=labels)
            plt.ylabel('适应度')
            plt.title('不同迭代的适应度分布')
            plt.xticks(rotation=45)

        # 最优控制点
        plt.subplot(2, 2, 4)
        if self.global_best_position is not None:
            plt.plot(self.global_best_position, 'ro-', linewidth=2, markersize=6)
            plt.xlabel('控制点索引')
            plt.ylabel('温度 (°C)')
            plt.title('最优控制点')
            plt.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"收敛图表已保存到 {save_path}")

        plt.show()

    def _merge_fixed_prefix_and_optimized_sequence(self, optimized_sequence: np.ndarray) -> np.ndarray:
        """
        合并固定前缀和优化序列

        Args:
            optimized_sequence: PSO优化生成的温度序列

        Returns:
            合并后的完整温度序列
        """
        if not self.fixed_prefix_enabled:
            return optimized_sequence

        # 直接拼接固定前缀和优化序列
        merged_sequence = np.concatenate([self.fixed_prefix_data, optimized_sequence])

        logger.debug(f"合并序列: 固定前缀 {len(self.fixed_prefix_data):,} + 优化序列 {len(optimized_sequence):,} = 总长度 {len(merged_sequence):,}")

        return merged_sequence

    def _apply_fixed_prefix_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用固定前缀约束（确保前N维保持不变）

        Args:
            sequence: 输入序列

        Returns:
            应用约束后的序列
        """
        if not self.fixed_prefix_enabled:
            return sequence

        # 如果序列长度小于固定前缀长度，直接返回固定前缀的对应部分
        if len(sequence) <= self.fixed_prefix_length:
            return self.fixed_prefix_data[:len(sequence)].copy()

        # 替换前N个数据点为固定前缀
        constrained_sequence = sequence.copy()
        constrained_sequence[:self.fixed_prefix_length] = self.fixed_prefix_data

        return constrained_sequence

    def get_fixed_prefix_info(self) -> Dict:
        """
        获取固定前缀信息

        Returns:
            固定前缀信息字典
        """
        if not self.fixed_prefix_enabled:
            return {'enabled': False}

        return {
            'enabled': True,
            'length': self.fixed_prefix_length,
            'min_temp': float(np.min(self.fixed_prefix_data)),
            'max_temp': float(np.max(self.fixed_prefix_data)),
            'mean_temp': float(np.mean(self.fixed_prefix_data)),
            'metadata': self.fixed_prefix_metadata
        }


def main():
    """测试PSO优化器功能"""
    # 简单的测试适应度函数
    def test_fitness(sequence):
        # 简单的适应度函数：偏好温度在50-100之间的序列
        target_temp = 75
        fitness = -np.mean((sequence - target_temp) ** 2)  # 负均方误差
        return fitness

    # 创建PSO优化器
    optimizer = PSOOptimizer()

    # 执行优化
    results = optimizer.optimize(test_fitness)

    print(f"优化完成！最佳适应度: {results['best_fitness']:.4f}")
    print(f"迭代次数: {results['total_iterations']}")

    # 绘制收敛曲线
    optimizer.plot_convergence()


if __name__ == "__main__":
    main()
