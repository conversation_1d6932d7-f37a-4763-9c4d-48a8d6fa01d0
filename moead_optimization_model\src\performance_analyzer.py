#!/usr/bin/env python3
"""
性能分析器模块

该模块负责：
1. 分析优化结果的性能指标
2. 与原始数据进行对比分析
3. 生成性能评估报告
4. 可视化性能对比
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from typing import Dict, List, Tuple, Optional
import logging
import yaml

logger = logging.getLogger(__name__)


class PerformanceAnalyzer:
    """性能分析器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化性能分析器
        
        Args:
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.analysis_results = {}
        
    def analyze_optimization_performance(self, optimization_results: Dict) -> Dict:
        """
        分析优化性能
        
        Args:
            optimization_results: 优化结果字典
            
        Returns:
            性能分析结果
        """
        performance_metrics = {}
        
        # 收敛性分析
        convergence_analysis = self._analyze_convergence(optimization_results)
        performance_metrics['convergence'] = convergence_analysis
        
        # 多样性分析
        diversity_analysis = self._analyze_diversity(optimization_results)
        performance_metrics['diversity'] = diversity_analysis
        
        # 稳定性分析
        stability_analysis = self._analyze_stability(optimization_results)
        performance_metrics['stability'] = stability_analysis
        
        # 效率分析
        efficiency_analysis = self._analyze_efficiency(optimization_results)
        performance_metrics['efficiency'] = efficiency_analysis
        
        return performance_metrics
    
    def _analyze_convergence(self, optimization_results: Dict) -> Dict:
        """分析收敛性能"""
        best_fitness_history = optimization_results['best_fitness_history']
        
        if len(best_fitness_history) < 2:
            return {'convergence_rate': 0, 'final_improvement': 0}
        
        # 收敛速度
        initial_fitness = best_fitness_history[0]
        final_fitness = best_fitness_history[-1]
        total_improvement = final_fitness - initial_fitness
        
        # 计算收敛率（50%改善所需的迭代次数）
        target_improvement = total_improvement * 0.5
        convergence_iteration = len(best_fitness_history)
        
        for i, fitness in enumerate(best_fitness_history):
            if fitness - initial_fitness >= target_improvement:
                convergence_iteration = i
                break
        
        convergence_rate = convergence_iteration / len(best_fitness_history)
        
        # 最终改善幅度
        relative_improvement = total_improvement / abs(initial_fitness) if initial_fitness != 0 else 0
        
        return {
            'convergence_rate': convergence_rate,
            'total_improvement': total_improvement,
            'relative_improvement': relative_improvement,
            'final_fitness': final_fitness,
            'iterations_to_50_percent': convergence_iteration
        }
    
    def _analyze_diversity(self, optimization_results: Dict) -> Dict:
        """分析粒子群多样性"""
        diversity_history = optimization_results['diversity_history']
        
        if not diversity_history:
            return {'diversity_maintained': False}
        
        initial_diversity = diversity_history[0]
        final_diversity = diversity_history[-1]
        min_diversity = min(diversity_history)
        max_diversity = max(diversity_history)
        
        # 多样性保持率
        diversity_retention = final_diversity / initial_diversity if initial_diversity > 0 else 0
        
        # 多样性变化趋势
        diversity_trend = np.polyfit(range(len(diversity_history)), diversity_history, 1)[0]
        
        return {
            'initial_diversity': initial_diversity,
            'final_diversity': final_diversity,
            'min_diversity': min_diversity,
            'max_diversity': max_diversity,
            'diversity_retention': diversity_retention,
            'diversity_trend': diversity_trend,
            'diversity_maintained': diversity_retention > 0.1
        }
    
    def _analyze_stability(self, optimization_results: Dict) -> Dict:
        """分析算法稳定性"""
        best_fitness_history = optimization_results['best_fitness_history']
        
        if len(best_fitness_history) < 10:
            return {'stability_score': 0}
        
        # 计算后期稳定性（最后20%的迭代）
        late_stage_start = int(len(best_fitness_history) * 0.8)
        late_stage_fitness = best_fitness_history[late_stage_start:]
        
        # 稳定性指标：后期适应度的变异系数
        late_stage_std = np.std(late_stage_fitness)
        late_stage_mean = np.mean(late_stage_fitness)
        
        stability_score = 1.0 / (1.0 + late_stage_std / abs(late_stage_mean)) if late_stage_mean != 0 else 0
        
        # 单调性检查
        monotonic_improvements = sum(1 for i in range(1, len(best_fitness_history)) 
                                   if best_fitness_history[i] >= best_fitness_history[i-1])
        monotonicity = monotonic_improvements / (len(best_fitness_history) - 1)
        
        return {
            'stability_score': stability_score,
            'late_stage_std': late_stage_std,
            'monotonicity': monotonicity,
            'is_stable': stability_score > 0.8
        }
    
    def _analyze_efficiency(self, optimization_results: Dict) -> Dict:
        """分析算法效率"""
        total_iterations = optimization_results['total_iterations']
        max_iterations = optimization_results.get('max_iterations', total_iterations)
        
        # 迭代效率
        iteration_efficiency = total_iterations / max_iterations if max_iterations > 0 else 1.0
        
        # 适应度改善效率
        best_fitness_history = optimization_results['best_fitness_history']
        if len(best_fitness_history) > 1:
            improvement_per_iteration = (best_fitness_history[-1] - best_fitness_history[0]) / len(best_fitness_history)
        else:
            improvement_per_iteration = 0
        
        return {
            'iteration_efficiency': iteration_efficiency,
            'improvement_per_iteration': improvement_per_iteration,
            'early_termination': total_iterations < max_iterations,
            'converged': optimization_results.get('converged', False)
        }
    
    def compare_with_baseline(self, optimized_sequence: np.ndarray, 
                            baseline_sequences: List[np.ndarray],
                            sequence_generator) -> Dict:
        """
        与基线序列进行对比分析
        
        Args:
            optimized_sequence: 优化后的序列
            baseline_sequences: 基线序列列表
            sequence_generator: 序列生成器
            
        Returns:
            对比分析结果
        """
        comparison_results = {}
        
        # 分析优化序列
        opt_analysis = sequence_generator.analyze_sequence_quality(optimized_sequence)
        
        # 分析基线序列
        baseline_analyses = []
        for seq in baseline_sequences:
            analysis = sequence_generator.analyze_sequence_quality(seq)
            baseline_analyses.append(analysis)
        
        # 计算对比指标
        comparison_metrics = self._calculate_comparison_metrics(opt_analysis, baseline_analyses)
        
        comparison_results['optimized_analysis'] = opt_analysis
        comparison_results['baseline_analyses'] = baseline_analyses
        comparison_results['comparison_metrics'] = comparison_metrics
        
        return comparison_results
    
    def _calculate_comparison_metrics(self, opt_analysis: Dict, baseline_analyses: List[Dict]) -> Dict:
        """计算对比指标"""
        metrics = {}
        
        # 关键指标对比
        key_metrics = ['mean_temperature', 'std_temperature', 'max_change_rate', 'smoothness']
        
        for metric in key_metrics:
            opt_value = opt_analysis.get(metric, 0)
            baseline_values = [analysis.get(metric, 0) for analysis in baseline_analyses]
            
            if baseline_values:
                baseline_mean = np.mean(baseline_values)
                baseline_std = np.std(baseline_values)
                
                # 计算改善程度
                if metric in ['smoothness']:  # 越大越好的指标
                    improvement = (opt_value - baseline_mean) / baseline_mean if baseline_mean != 0 else 0
                else:  # 越小越好的指标（如变化率）
                    improvement = (baseline_mean - opt_value) / baseline_mean if baseline_mean != 0 else 0
                
                # 计算Z分数
                z_score = (opt_value - baseline_mean) / baseline_std if baseline_std > 0 else 0
                
                metrics[metric] = {
                    'optimized_value': opt_value,
                    'baseline_mean': baseline_mean,
                    'baseline_std': baseline_std,
                    'improvement': improvement,
                    'z_score': z_score,
                    'percentile': stats.percentileofscore(baseline_values, opt_value)
                }
        
        return metrics
    
    def generate_performance_report(self, optimization_results: Dict, 
                                  comparison_results: Dict = None) -> str:
        """
        生成性能评估报告
        
        Args:
            optimization_results: 优化结果
            comparison_results: 对比结果
            
        Returns:
            报告文本
        """
        report = []
        report.append("=" * 60)
        report.append("温度序列PSO优化性能评估报告")
        report.append("=" * 60)
        
        # 优化性能分析
        performance_metrics = self.analyze_optimization_performance(optimization_results)
        
        report.append("\n1. 优化算法性能分析")
        report.append("-" * 30)
        
        # 收敛性分析
        conv = performance_metrics['convergence']
        report.append(f"收敛性能:")
        report.append(f"  - 最终适应度: {conv['final_fitness']:.6f}")
        report.append(f"  - 总体改善: {conv['total_improvement']:.6f}")
        report.append(f"  - 相对改善: {conv['relative_improvement']:.2%}")
        report.append(f"  - 50%改善所需迭代: {conv['iterations_to_50_percent']}")
        
        # 多样性分析
        div = performance_metrics['diversity']
        report.append(f"\n多样性维持:")
        report.append(f"  - 初始多样性: {div['initial_diversity']:.6f}")
        report.append(f"  - 最终多样性: {div['final_diversity']:.6f}")
        report.append(f"  - 多样性保持率: {div['diversity_retention']:.2%}")
        report.append(f"  - 多样性维持: {'是' if div['diversity_maintained'] else '否'}")
        
        # 稳定性分析
        stab = performance_metrics['stability']
        report.append(f"\n算法稳定性:")
        report.append(f"  - 稳定性评分: {stab['stability_score']:.4f}")
        report.append(f"  - 单调性: {stab['monotonicity']:.2%}")
        report.append(f"  - 是否稳定: {'是' if stab['is_stable'] else '否'}")
        
        # 效率分析
        eff = performance_metrics['efficiency']
        report.append(f"\n算法效率:")
        report.append(f"  - 迭代效率: {eff['iteration_efficiency']:.2%}")
        report.append(f"  - 每迭代改善: {eff['improvement_per_iteration']:.6f}")
        report.append(f"  - 提前收敛: {'是' if eff['early_termination'] else '否'}")
        
        # 对比分析（如果提供）
        if comparison_results:
            report.append("\n2. 与基线序列对比分析")
            report.append("-" * 30)
            
            comp_metrics = comparison_results['comparison_metrics']
            for metric, data in comp_metrics.items():
                report.append(f"\n{metric}:")
                report.append(f"  - 优化值: {data['optimized_value']:.4f}")
                report.append(f"  - 基线均值: {data['baseline_mean']:.4f}")
                report.append(f"  - 改善程度: {data['improvement']:.2%}")
                report.append(f"  - 百分位数: {data['percentile']:.1f}%")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)
    
    def plot_performance_comparison(self, optimization_results: Dict, 
                                  comparison_results: Dict = None,
                                  save_path: str = None):
        """
        绘制性能对比图表
        
        Args:
            optimization_results: 优化结果
            comparison_results: 对比结果
            save_path: 保存路径
        """
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('PSO优化性能分析', fontsize=16)
        
        # 1. 收敛曲线
        best_fitness = optimization_results['best_fitness_history']
        axes[0, 0].plot(best_fitness, 'b-', linewidth=2)
        axes[0, 0].set_title('适应度收敛曲线')
        axes[0, 0].set_xlabel('迭代次数')
        axes[0, 0].set_ylabel('适应度')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 多样性变化
        diversity = optimization_results['diversity_history']
        axes[0, 1].plot(diversity, 'g-', linewidth=2)
        axes[0, 1].set_title('粒子群多样性')
        axes[0, 1].set_xlabel('迭代次数')
        axes[0, 1].set_ylabel('多样性')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 最优温度序列
        best_sequence = optimization_results['best_temperature_sequence']
        axes[0, 2].plot(best_sequence, 'r-', linewidth=2)
        axes[0, 2].set_title('最优温度序列')
        axes[0, 2].set_xlabel('时间步')
        axes[0, 2].set_ylabel('温度 (°C)')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. 性能指标雷达图
        performance_metrics = self.analyze_optimization_performance(optimization_results)
        self._plot_performance_radar(axes[1, 0], performance_metrics)
        
        # 5. 对比分析（如果有）
        if comparison_results:
            self._plot_comparison_metrics(axes[1, 1], comparison_results)
        else:
            axes[1, 1].text(0.5, 0.5, '无对比数据', ha='center', va='center', 
                           transform=axes[1, 1].transAxes, fontsize=14)
            axes[1, 1].set_title('对比分析')
        
        # 6. 适应度分布
        if 'fitness_history' in optimization_results:
            final_fitness = optimization_results['fitness_history'][-1]
            axes[1, 2].hist(final_fitness, bins=20, alpha=0.7, color='purple')
            axes[1, 2].axvline(optimization_results['best_fitness'], color='red', 
                              linestyle='--', linewidth=2, label='最佳适应度')
            axes[1, 2].set_title('最终适应度分布')
            axes[1, 2].set_xlabel('适应度')
            axes[1, 2].set_ylabel('频次')
            axes[1, 2].legend()
            axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"性能分析图表已保存到 {save_path}")
        
        plt.show()
    
    def _plot_performance_radar(self, ax, performance_metrics: Dict):
        """绘制性能雷达图"""
        # 提取关键指标
        metrics = {
            '收敛速度': 1 - performance_metrics['convergence']['convergence_rate'],
            '多样性维持': performance_metrics['diversity']['diversity_retention'],
            '算法稳定性': performance_metrics['stability']['stability_score'],
            '迭代效率': performance_metrics['efficiency']['iteration_efficiency']
        }
        
        # 雷达图数据
        categories = list(metrics.keys())
        values = list(metrics.values())
        
        # 角度
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        values += values[:1]  # 闭合图形
        angles += angles[:1]
        
        # 绘制雷达图
        ax.plot(angles, values, 'o-', linewidth=2, color='blue')
        ax.fill(angles, values, alpha=0.25, color='blue')
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(categories)
        ax.set_ylim(0, 1)
        ax.set_title('性能指标雷达图')
        ax.grid(True)
    
    def _plot_comparison_metrics(self, ax, comparison_results: Dict):
        """绘制对比指标"""
        comp_metrics = comparison_results['comparison_metrics']
        
        metrics = list(comp_metrics.keys())
        improvements = [comp_metrics[m]['improvement'] for m in metrics]
        
        colors = ['green' if imp > 0 else 'red' for imp in improvements]
        
        bars = ax.bar(metrics, improvements, color=colors, alpha=0.7)
        ax.set_title('与基线对比改善程度')
        ax.set_ylabel('改善程度')
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, imp in zip(bars, improvements):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{imp:.1%}', ha='center', va='bottom' if height > 0 else 'top')


def main():
    """测试性能分析器"""
    # 创建模拟优化结果
    test_results = {
        'best_fitness_history': np.cumsum(np.random.exponential(0.1, 100)) + np.random.normal(0, 0.01, 100),
        'diversity_history': np.exp(-np.linspace(0, 3, 100)) + 0.1,
        'best_temperature_sequence': np.random.uniform(20, 150, 1000),
        'total_iterations': 100,
        'converged': True
    }
    
    analyzer = PerformanceAnalyzer()
    
    # 分析性能
    performance = analyzer.analyze_optimization_performance(test_results)
    print("性能分析结果:")
    for key, value in performance.items():
        print(f"{key}: {value}")
    
    # 生成报告
    report = analyzer.generate_performance_report(test_results)
    print("\n" + report)


if __name__ == "__main__":
    main()
