#!/usr/bin/env python3
"""
化工车间温度序列MOEA/D多目标优化系统

基于MOEA/D(Multi-Objective Evolutionary Algorithm based on Decomposition)的温度序列多目标优化系统，
同时优化多个质量指标找到最优的温度序列。

主要模块：
- data_processor: 数据处理和成对比较数据集构建
- feature_extractor: 特征提取（ResNet+GRU+PCA混合架构）
- sequence_classifier: 序列比较分类器
- moead_optimizer: MOEA/D多目标优化算法实现
- fitness_evaluator: 多目标适应度评估器
- sequence_generator: 温度序列生成器
- utils: 工具函数

作者: AI Assistant
版本: 2.0.0
"""

__version__ = "2.0.0"
__author__ = "AI Assistant"
__email__ = ""
__description__ = "基于MOEA/D算法的化工车间温度序列多目标优化系统"

# 导入主要类和函数
from .data_processor import DataProcessor
from .feature_extractor import FeatureExtractor
from .sequence_classifier import SequenceClassifier
from .moead_optimizer import MOEADOptimizer
from .fitness_evaluator import FitnessEvaluator, MultiObjectiveFunctions
from .sequence_generator import SequenceGenerator
from .utils import load_config, setup_logging, create_directories

__all__ = [
    "DataProcessor",
    "FeatureExtractor",
    "SequenceClassifier",
    "MOEADOptimizer",
    "FitnessEvaluator",
    "MultiObjectiveFunctions",
    "SequenceGenerator",
    "load_config",
    "setup_logging",
    "create_directories"
]
