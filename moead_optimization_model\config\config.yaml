# 化工车间温度序列PSO优化系统配置文件

# 数据配置
data:
  # 数据文件路径
  data_dir: "data/Esterification"
  sample_file_pattern: "Sample_{}.xlsx"
  label_files:
    label_1: "label_1.xlsx"
  
  # 数据处理参数
  max_sequence_length: 50000  # 最大序列长度
  min_sequence_length: 1000   # 最小序列长度
  downsample_factor: 10       # 下采样因子
  
  # 质量评分权重 (简化为只使用label_1)
  quality_weights:
    label_1: 1.0  # 质量指标权重 (越低越好)

  # 标签优化方向说明
  # label_1: 越低越好 - 在质量评分计算中会自动反转
  # 注意：权重可通过命令行参数 --label1-weight 覆盖

# 特征提取配置
feature_extraction:
  # 特征提取器类型选择
  extractor_type: "resnet_gru"  # "resnet_gru" 或 "lstm"

  # 统计特征参数
  statistical_features:
    enable: true
    # 性能优化选项
    fast_mode: true  # 启用快速模式，跳过耗时的特征
    skip_autocorr: true  # 跳过自相关特征（最耗时）
    skip_frequency: true  # 跳过频域特征
    max_sequence_length: 1000  # 限制序列长度
    autocorr_max_lags: 50
    frequency_analysis: true

  # ResNet+GRU时序特征参数（新架构）
  resnet_gru_features:
    enable: true
    hidden_size: 64
    num_layers: 2
    dropout: 0.2
    sequence_length: 1000  # 输入序列长度
    num_residual_blocks: 3

    # GPU优化参数
    gpu_optimization:
      enable: true  # 启用GPU优化
      batch_inference: true  # 批量推理
      memory_efficient: true  # 内存高效模式
      compile_model: false  # 模型编译（PyTorch 2.0+）

  # LSTM时序特征参数（向后兼容）
  lstm_features:
    enable: false  # 默认关闭，使用ResNet+GRU
    hidden_size: 64
    num_layers: 2
    dropout: 0.2
    sequence_length: 100  # LSTM输入序列长度

    # GPU优化参数
    gpu_optimization:
      enable: true  # 启用GPU优化
      batch_inference: true  # 批量推理
      memory_efficient: true  # 内存高效模式
      compile_model: false  # 模型编译（PyTorch 2.0+）

  # PCA降维配置
  pca:
    enable: true
    n_components: 50  # 降维后的维度

  # 特征标准化
  standardization:
    enable: true
    method: "standard"  # standard, minmax, robust

# 分类器配置
classifier:
  # SVM参数
  svm:
    kernel: "rbf"
    C: 1.0
    gamma: "scale"
    probability: true
  
  # 训练参数
  training:
    test_size: 0.2
    cv_folds: 5
    random_state: 42
    
  # 数据增强
  data_augmentation:
    enable: true
    noise_level: 0.01
    augmentation_factor: 2

# PSO优化配置
pso:
  # 基本参数
  swarm_size: 30
  max_iterations: 200
  
  # PSO参数
  w: 0.9          # 惯性权重
  c1: 2.0         # 个体学习因子
  c2: 2.0         # 社会学习因子
  
  # 自适应参数
  adaptive:
    enable: true
    w_min: 0.4
    w_max: 0.9
  
  # 收敛条件
  convergence:
    tolerance: 1e-6
    patience: 50    # 连续多少代无改善则停止
  
  # 温度序列参数 (支持变长序列优化)
  temperature_sequence:
    # 固定参数 (基于21个样本数据集分析优化 - 2025年7月19日更新)
    control_points: 200       # 控制点数量 (增加以更好捕捉长序列特性)
    min_temperature: 13.0     # 最低温度 (°C) - 基于真实数据最小值13.1°C
    max_temperature: 152.0    # 最高温度 (°C) - 基于真实数据最大值151.3°C
    max_change_rate: 0.10     # 最大变化率 (°C/step) - 基于95%分位数0.10

    # 真实数据统计特性约束（基于21个训练样本分析修正）
    target_mean_temperature: 132.3    # 目标平均温度 (°C) - 修正为实际训练数据均值
    target_std_temperature: 1.3       # 目标温度标准差 (°C) - 修正为实际训练数据标准差
    temperature_distribution_weight: 0.5  # 分布匹配权重 - 增加权重

    # 变长序列参数 (基于数据集分析: 18809-92003)
    variable_length:
      enable: true            # 启用变长序列优化
      min_length: 18809       # 最小序列长度 (Sample_2)
      max_length: 92003       # 最大序列长度 (Sample_14)
      default_length: 32444   # 默认序列长度 (中位数)

      # 长度优化参数
      length_weight: 0.1      # 长度优化在适应度中的权重
      length_penalty_factor: 0.05  # 长度偏离惩罚因子

      # 插值策略
      interpolation_method: "cubic_spline"  # 插值方法: linear, cubic_spline
      boundary_handling: "natural"         # 边界处理: natural, clamped

      # 业务趋势约束 (基于21个样本数据集分析: 100%样本为上升趋势)
      enforce_business_trend: true          # 强制执行业务趋势约束
      expected_temp_rise_min: 88            # 最小温度上升幅度 (°C) - 基于Sample_19的88.3°C
      expected_temp_rise_max: 138           # 最大温度上升幅度 (°C) - 基于真实数据范围调整

      # 温度分布特性约束
      enforce_distribution_matching: true    # 强制执行分布匹配
      low_temp_region_ratio: 0.15          # 低温区域比例 (13-50°C)
      medium_temp_region_ratio: 0.25       # 中温区域比例 (50-100°C)
      high_temp_region_ratio: 0.60         # 高温区域比例 (100-152°C)

      # 训练数据约束强化（基于问题诊断添加）
      enable_training_constraints: true     # 启用训练数据约束
      constraint_strength: 0.9             # 约束强度（0-1）
      enforce_starting_temp_distribution: true  # 强制执行起始温度分布

# MOEA/D多目标优化配置
moead:
  # 基本参数
  population_size: 100      # 种群大小
  max_generations: 200      # 最大代数
  neighbor_size: 20         # 邻域大小

  # 差分进化参数
  F: 0.5                    # 缩放因子
  CR: 0.9                   # 交叉概率

  # 权重向量生成
  weight_generation:
    method: "uniform"       # uniform, random

  # 分解策略
  decomposition:
    method: "tchebycheff"   # tchebycheff, weighted_sum, pbi

  # 收敛条件
  convergence:
    tolerance: 1e-6
    patience: 50

  # 外部档案管理
  external_archive:
    max_size: 200           # 最大档案大小
    update_strategy: "non_dominated"  # non_dominated, crowding_distance

# 多目标函数配置
multi_objective:
  # 目标函数权重
  objective_weights:
    f1_label1: 1.0          # 最小化label_1指标的权重
    f2_label2: 1.0          # 最大化label_2指标的权重
    f3_smoothness: 1.0      # 最大化平滑性指标的权重
    f4_distribution: 1.0    # 最大化分布匹配度指标的权重

  # 平滑性指标配置
  smoothness:
    method: "fourier_transform"  # 计算方法
    low_freq_threshold: 0.1      # 低频阈值
    high_freq_penalty: 0.5       # 高频惩罚系数

  # 目标函数标准化
  normalization:
    enable: true
    method: "min_max"       # min_max, z_score

# 基于分类学习的适应度评估配置
classification_fitness:
  # 帕累托分类
  use_pareto_classification: true

  # 目标函数权重（用于综合评分）
  objective_weights: [1.0, 1.0, 1.0, 1.0]  # [f1, f2, f3, f4]

  # 缓存配置
  cache_enabled: true

  # 分类器配置
  classifier_config:
    model_type: "binary"    # binary, multi_class
    training_strategy: "pareto_based"  # pareto_based, score_based

# 基于分类学习的标签预测器配置
classification_predictor:
  # 分箱配置
  label1_bins: 5          # label_1分箱数量
  label2_bins: 5          # label_2分箱数量

  # 分类器配置
  classifier_type: "random_forest"  # random_forest, svm, gradient_boosting

  # 随机森林参数
  random_forest:
    n_estimators: 100
    max_depth: 10
    random_state: 42

  # 交叉验证
  cross_validation:
    cv_folds: 5

  # 特征重要性分析
  feature_importance:
    enable: true
    top_features: 20

    # 向后兼容 (变长模式禁用时使用)
    sequence_length: 1000     # 固定序列长度 (变长模式禁用时)

# 模型保存配置
model:
  # 保存路径
  save_dir: "models"
  
  # 模型文件命名
  classifier_name: "sequence_classifier"
  feature_extractor_name: "feature_extractor"
  
  # 保存选项
  save_training_history: true
  save_feature_importance: true

# 结果输出配置
output:
  # 结果保存路径
  results_dir: "results"
  
  # 可视化配置
  visualization:
    enable: true
    save_plots: true
    plot_formats: ["png", "pdf"]
    dpi: 300
  
  # 报告配置
  report:
    enable: true
    include_convergence_plot: true
    include_temperature_profile: true
    include_comparison_analysis: true

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "optimization.log"
  console: true

# 计算资源配置 (GPU优化版本)
computing:
  # 并行处理
  n_jobs: -1  # -1表示使用所有可用CPU核心

  # GPU配置
  gpu:
    enable: true  # 启用GPU加速
    device_id: 0  # GPU设备ID
    auto_detect: true  # 自动检测GPU可用性
    fallback_to_cpu: true  # GPU不可用时回退到CPU

    # GPU内存管理
    memory_optimization: true  # 启用内存优化
    clear_cache_frequency: 10  # 每N次操作清理一次缓存
    max_memory_usage: 0.8  # 最大GPU内存使用比例

    # CUDA配置
    cuda_version: "12.1"  # 期望的CUDA版本
    mixed_precision: false  # 混合精度训练（实验性）

  # CPU配置
  cpu:
    n_jobs: -1  # CPU并行作业数
    memory_limit: "8GB"  # CPU内存限制

  # 批处理配置
  batch_processing:
    batch_size: 32  # 批处理大小
    adaptive_batch_size: true  # 自适应批大小
    max_batch_size: 128  # 最大批大小

# 实验配置
experiment:
  # 随机种子
  random_seed: 42
  
  # 实验名称
  name: "chemical_temperature_optimization"
  
  # 版本控制
  version: "1.0.0"
  
  # 描述
  description: "基于PSO算法的化工车间温度序列优化系统"
