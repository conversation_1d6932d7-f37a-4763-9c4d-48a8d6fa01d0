#!/usr/bin/env python3
"""
化工车间温度序列PSO优化系统 - 主执行脚本

该脚本提供完整的端到端执行流程：
1. 数据预处理和分类器训练
2. PSO优化执行
3. 结果分析和可视化
4. 报告生成
"""

import os
import sys
import argparse
import logging
import subprocess
from datetime import datetime

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils import load_config, setup_logging, print_system_info, create_directories


def validate_weights(label1_weight):
    """
    验证权重参数 (简化为只验证label1)

    Args:
        label1_weight: label_1权重

    Returns:
        验证是否通过
    """
    if label1_weight is not None and label1_weight < 0:
        print("❌ label1-weight必须为非负数")
        return False

    if label1_weight is not None and label1_weight == 0:
        print("❌ label1-weight必须大于0")
        return False

    return True


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='化工车间温度序列PSO优化系统')
    
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--mode', type=str, choices=['train', 'optimize', 'full'], 
                       default='full', help='执行模式：train(仅训练), optimize(仅优化), full(完整流程)')
    parser.add_argument('--data-dir', type=str, default=None,
                       help='数据目录路径（覆盖配置文件设置）')
    parser.add_argument('--model-dir', type=str, default='models',
                       help='模型目录路径')
    parser.add_argument('--classifier-model', type=str, default=None,
                       help='自定义分类器模型文件路径（覆盖默认路径）')
    parser.add_argument('--feature-extractor-model', type=str, default=None,
                       help='自定义特征提取器模型文件路径（覆盖默认路径）')
    parser.add_argument('--load-model', type=str, default=None,
                       help='自定义模型目录路径（同时指定分类器和特征提取器的目录）')
    parser.add_argument('--output-dir', type=str, default='results',
                       help='结果输出目录')
    parser.add_argument('--max-iterations', type=int, default=None,
                       help='PSO最大迭代次数')
    parser.add_argument('--swarm-size', type=int, default=None,
                       help='PSO粒子群大小')
    parser.add_argument('--evaluation-strategy', type=str,
                       choices=['single', 'multiple', 'ensemble'], default='ensemble',
                       help='适应度评估策略')
    parser.add_argument('--label1-weight', type=float, default=None,
                       help='label_1的权重（越低越好，默认1.0）')
    parser.add_argument('--fixed-prefix-file', type=str, default=None,
                       help='固定前缀Excel文件路径（单列温度数据）')
    parser.add_argument('--save-plots', action='store_true',
                       help='保存所有图表')
    parser.add_argument('--augment-data', action='store_true',
                       help='启用数据增强')
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出模式')
    parser.add_argument('--skip-training', action='store_true',
                       help='跳过训练步骤（使用现有模型）')
    
    return parser.parse_args()


def run_training(args):
    """
    执行分类器训练
    
    Args:
        args: 命令行参数
        
    Returns:
        训练是否成功
    """
    print("\n" + "=" * 60)
    print("阶段1: 分类器训练")
    print("=" * 60)
    
    # 构建训练命令
    train_cmd = [
        sys.executable, 'train_classifier.py',
        '--config', args.config,
        '--output-dir', args.model_dir
    ]
    
    if args.data_dir:
        train_cmd.extend(['--data-dir', args.data_dir])
    if args.label1_weight is not None:
        train_cmd.extend(['--label1-weight', str(args.label1_weight)])
    if args.save_plots:
        train_cmd.append('--save-plots')
    if args.augment_data:
        train_cmd.append('--augment-data')
    if args.verbose:
        train_cmd.append('--verbose')
    
    # 执行训练
    try:
        result = subprocess.run(train_cmd, check=True, capture_output=False)
        print("✓ 分类器训练完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ 分类器训练失败: {e}")
        return False


def run_optimization(args):
    """
    执行PSO优化

    Args:
        args: 命令行参数

    Returns:
        优化是否成功
    """
    print("\n" + "=" * 60)
    print("阶段2: PSO优化")
    print("=" * 60)

    # 确定模型目录
    model_dir = args.load_model if args.load_model else args.model_dir

    # 构建优化命令
    opt_cmd = [
        sys.executable, 'run_optimization.py',
        '--config', args.config,
        '--model-dir', model_dir,
        '--output-dir', args.output_dir,
        '--evaluation-strategy', args.evaluation_strategy
    ]
    
    # 添加自定义模型路径参数
    if args.classifier_model:
        opt_cmd.extend(['--classifier-model', args.classifier_model])
    if args.feature_extractor_model:
        opt_cmd.extend(['--feature-extractor-model', args.feature_extractor_model])

    # 添加权重参数
    if args.label1_weight is not None:
        opt_cmd.extend(['--label1-weight', str(args.label1_weight)])

    # 添加固定前缀参数
    if args.fixed_prefix_file:
        opt_cmd.extend(['--fixed-prefix-file', args.fixed_prefix_file])

    if args.max_iterations:
        opt_cmd.extend(['--max-iterations', str(args.max_iterations)])
    if args.swarm_size:
        opt_cmd.extend(['--swarm-size', str(args.swarm_size)])
    # 注意：run_optimization.py不支持--save-plots参数，已简化输出
    if args.save_plots:
        print("💡 提示：PSO优化阶段已简化输出，--save-plots参数在此阶段不生效")
    if args.verbose:
        opt_cmd.append('--verbose')
    
    # 执行优化
    try:
        result = subprocess.run(opt_cmd, check=True, capture_output=False)
        print("✓ PSO优化完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ PSO优化失败: {e}")
        return False


def check_prerequisites(args):
    """
    检查运行前提条件
    
    Args:
        args: 命令行参数
        
    Returns:
        检查是否通过
    """
    print("检查运行前提条件...")
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"✗ 配置文件不存在: {args.config}")
        return False
    
    # 检查数据目录
    try:
        config = load_config(args.config)
        data_dir = args.data_dir or config['data']['data_dir']
        
        if not os.path.exists(data_dir):
            print(f"✗ 数据目录不存在: {data_dir}")
            return False
        
        # 检查必要的数据文件 (简化为只检查label_1.xlsx)
        required_files = ['label_1.xlsx']
        for file in required_files:
            file_path = os.path.join(data_dir, file)
            if not os.path.exists(file_path):
                print(f"✗ 必要数据文件不存在: {file_path}")
                return False
        
        # 检查样本文件
        sample_files = [f for f in os.listdir(data_dir) if f.startswith('Sample_') and f.endswith('.xlsx')]
        if len(sample_files) == 0:
            print(f"✗ 数据目录中没有找到样本文件: {data_dir}")
            return False
        
        print(f"✓ 找到 {len(sample_files)} 个样本文件")
        
    except Exception as e:
        print(f"✗ 配置文件或数据检查失败: {e}")
        return False
    
    # 如果是优化模式，检查模型文件
    if args.mode in ['optimize'] or args.skip_training:
        # 确定模型文件路径
        if args.classifier_model and args.feature_extractor_model:
            # 使用完全自定义的模型路径
            classifier_path = args.classifier_model
            feature_extractor_path = args.feature_extractor_model
            print(f"使用自定义模型文件:")
            print(f"  分类器: {classifier_path}")
            print(f"  特征提取器: {feature_extractor_path}")
        elif args.load_model:
            # 使用自定义模型目录
            classifier_path = os.path.join(args.load_model, f"{config['model']['classifier_name']}.joblib")
            feature_extractor_path = os.path.join(args.load_model, f"{config['model']['feature_extractor_name']}.joblib")
            print(f"使用自定义模型目录: {args.load_model}")
        else:
            # 使用默认模型目录
            classifier_path = os.path.join(args.model_dir, f"{config['model']['classifier_name']}.joblib")
            feature_extractor_path = os.path.join(args.model_dir, f"{config['model']['feature_extractor_name']}.joblib")
            print(f"使用默认模型目录: {args.model_dir}")

        # 检查文件是否存在
        if not os.path.exists(classifier_path):
            print(f"✗ 分类器模型文件不存在: {classifier_path}")
            print(f"   请确保模型文件存在，或使用以下选项指定正确路径:")
            print(f"   --classifier-model <path>  指定分类器模型文件")
            print(f"   --load-model <dir>         指定模型目录")
            return False

        if not os.path.exists(feature_extractor_path):
            print(f"✗ 特征提取器文件不存在: {feature_extractor_path}")
            print(f"   请确保模型文件存在，或使用以下选项指定正确路径:")
            print(f"   --feature-extractor-model <path>  指定特征提取器模型文件")
            print(f"   --load-model <dir>                 指定模型目录")
            return False

        print("✓ 模型文件检查通过")
    
    print("✓ 所有前提条件检查通过")
    return True


def generate_final_report(args):
    """
    生成最终报告
    
    Args:
        args: 命令行参数
    """
    print("\n" + "=" * 60)
    print("生成最终报告")
    print("=" * 60)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_path = os.path.join(args.output_dir, f"final_report_{timestamp}.txt")
    
    try:
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("化工车间温度序列PSO优化系统 - 执行报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"执行模式: {args.mode}\n")
            f.write(f"配置文件: {args.config}\n")
            f.write(f"模型目录: {args.model_dir}\n")
            f.write(f"输出目录: {args.output_dir}\n")
            f.write("\n执行参数:\n")
            for key, value in vars(args).items():
                f.write(f"  {key}: {value}\n")
            
            f.write("\n文件清单:\n")
            
            # 列出模型文件
            if os.path.exists(args.model_dir):
                f.write(f"\n模型文件 ({args.model_dir}):\n")
                for file in os.listdir(args.model_dir):
                    f.write(f"  - {file}\n")
            
            # 列出结果文件
            if os.path.exists(args.output_dir):
                f.write(f"\n结果文件 ({args.output_dir}):\n")
                for file in os.listdir(args.output_dir):
                    f.write(f"  - {file}\n")
        
        print(f"✓ 最终报告已生成: {report_path}")
        
    except Exception as e:
        print(f"✗ 生成最终报告失败: {e}")


def main():
    """主执行流程"""
    # 解析参数
    args = parse_arguments()
    
    # 打印系统信息
    print_system_info()
    
    # 创建必要目录
    create_directories([args.model_dir, args.output_dir])

    # 验证权重参数
    if not validate_weights(args.label1_weight):
        print("\n权重参数验证失败，程序退出")
        return False

    # 显示权重设置
    if args.label1_weight is not None:
        l1_weight = args.label1_weight
        print(f"\n⚖️ 质量权重设置:")
        print(f"  label_1权重: {l1_weight} (越低越好)")

    # 检查前提条件
    if not check_prerequisites(args):
        print("\n前提条件检查失败，程序退出")
        return False
    
    success = True
    
    try:
        # 根据模式执行相应流程
        if args.mode in ['train', 'full']:
            if not args.skip_training:
                success = run_training(args)
                if not success:
                    print("\n训练阶段失败，程序退出")
                    return False
            else:
                print("\n跳过训练阶段（使用现有模型）")
        
        if args.mode in ['optimize', 'full']:
            success = run_optimization(args)
            if not success:
                print("\n优化阶段失败，程序退出")
                return False
        
        # 生成最终报告
        generate_final_report(args)
        
        print("\n" + "=" * 60)
        print("🎉 系统执行完成！")
        print("=" * 60)
        print(f"模型文件位置: {args.model_dir}")
        print(f"结果文件位置: {args.output_dir}")
        
        if args.mode == 'full':
            print("\n完整流程已执行：")
            print("  ✓ 数据处理和特征提取")
            print("  ✓ 分类器训练")
            print("  ✓ PSO优化")
            print("  ✓ 结果分析和可视化")
        
        print(f"\n推荐下一步操作：")
        print(f"  1. 查看优化结果: {args.output_dir}")
        print(f"  2. 分析最优温度序列")
        print(f"  3. 根据需要调整参数重新优化")
        
        return True
        
    except KeyboardInterrupt:
        print("\n\n用户中断执行")
        return False
    except Exception as e:
        print(f"\n执行过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
