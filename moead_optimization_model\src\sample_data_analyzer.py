"""
Enhanced Sample Data Analyzer for MOEAD Optimization

This module provides comprehensive analysis of sample temperature data,
including statistical feature extraction, pattern analysis, and similarity calculations.
"""

import os
import json
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from scipy.stats import pearsonr
from scipy.spatial.distance import euclidean
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class StatRange:
    """Statistical range information"""
    mean: float
    std: float
    min: float
    max: float


@dataclass
class SampleStatistics:
    """Comprehensive sample statistics"""
    # Basic statistics
    initial_temp: float  # 起始温度
    final_temp: float    # 最终温度
    peak_temp: float     # 峰值温度
    peak_position: float # 峰值位置比例
    temp_range: float    # 温度范围
    total_change: float  # 总变化量
    
    # Stage changes (五阶段变化)
    stage_changes: List[float]  # 五阶段变化量
    
    # Change rate features
    max_increase: float     # 最大升温率
    max_decrease: float     # 最大降温率
    mean_change_rate: float # 平均变化率


@dataclass
class OverallStatistics:
    """Overall statistics across all samples"""
    initial_temp: StatRange    # 起始温度统计
    final_temp: StatRange      # 最终温度统计
    peak_temp: StatRange       # 峰值温度统计
    peak_position: StatRange   # 峰值位置统计
    total_change: StatRange    # 总变化量统计


@dataclass
class SimilarityMetrics:
    """Similarity metrics between sequences"""
    correlation: float              # 皮尔逊相关系数
    euclidean_similarity: float     # 欧几里得相似度
    initial_temp_similarity: float  # 起始温度相似度
    final_temp_similarity: float    # 最终温度相似度
    peak_temp_similarity: float     # 峰值温度相似度
    peak_position_similarity: float # 峰值位置相似度
    overall_similarity: float       # 综合相似度


class SampleDataAnalyzer:
    """Enhanced sample data analyzer with comprehensive analysis capabilities"""
    
    def __init__(self, data_dir: str = "data/Esterification"):
        """
        Initialize the analyzer
        
        Args:
            data_dir: Directory containing sample data files
        """
        self.data_dir = data_dir
        self.sample_data: Dict[str, np.ndarray] = {}
        self.sample_statistics: Dict[str, SampleStatistics] = {}
        self.overall_statistics: Optional[OverallStatistics] = None
        self.reference_template: Optional[np.ndarray] = None
        
    def load_sample_data(self) -> Dict[str, np.ndarray]:
        """
        Load all sample data files
        
        Returns:
            Dictionary mapping sample names to temperature sequences
        """
        try:
            sample_files = [f for f in os.listdir(self.data_dir) 
                          if f.startswith('Sample_') and f.endswith('.xlsx')]
            
            if len(sample_files) == 0:
                raise FileNotFoundError(f"No sample files found in {self.data_dir}")
            
            logger.info(f"Loading {len(sample_files)} sample files...")
            
            for file in sample_files:
                sample_name = file.replace('.xlsx', '')
                file_path = os.path.join(self.data_dir, file)
                
                try:
                    # Read Excel file without header
                    df = pd.read_excel(file_path, header=None)
                    # Extract temperature sequence
                    temp_sequence = df.iloc[:, 0].values.astype(float)
                    self.sample_data[sample_name] = temp_sequence
                    logger.info(f"Loaded {sample_name}: {len(temp_sequence)} data points")
                    
                except Exception as e:
                    logger.error(f"Failed to load {file}: {e}")
                    continue
            
            if len(self.sample_data) == 0:
                raise ValueError("No valid sample data loaded")
                
            logger.info(f"Successfully loaded {len(self.sample_data)} samples")
            return self.sample_data
            
        except Exception as e:
            logger.error(f"Error loading sample data: {e}")
            raise
    
    def calculate_sample_statistics(self) -> Dict[str, SampleStatistics]:
        """
        Calculate comprehensive statistics for each sample
        
        Returns:
            Dictionary mapping sample names to their statistics
        """
        if not self.sample_data:
            self.load_sample_data()
        
        logger.info("Calculating sample statistics...")
        
        for sample_name, sequence in self.sample_data.items():
            try:
                stats = self._calculate_single_sample_statistics(sequence)
                self.sample_statistics[sample_name] = stats
                
            except Exception as e:
                logger.error(f"Failed to calculate statistics for {sample_name}: {e}")
                continue
        
        # Calculate overall statistics
        self._calculate_overall_statistics()
        
        logger.info(f"Calculated statistics for {len(self.sample_statistics)} samples")
        return self.sample_statistics
    
    def _calculate_single_sample_statistics(self, sequence: np.ndarray) -> SampleStatistics:
        """Calculate statistics for a single sample sequence"""
        # Basic statistics
        initial_temp = float(sequence[0])
        final_temp = float(sequence[-1])
        peak_temp = float(np.max(sequence))
        peak_position = float(np.argmax(sequence)) / len(sequence)
        temp_range = float(np.max(sequence) - np.min(sequence))
        total_change = float(final_temp - initial_temp)
        
        # Calculate five-stage changes
        stage_changes = self._calculate_stage_changes(sequence)
        
        # Calculate change rates
        diff_sequence = np.diff(sequence)
        max_increase = float(np.max(diff_sequence))
        max_decrease = float(np.min(diff_sequence))
        mean_change_rate = float(np.mean(np.abs(diff_sequence)))
        
        return SampleStatistics(
            initial_temp=initial_temp,
            final_temp=final_temp,
            peak_temp=peak_temp,
            peak_position=peak_position,
            temp_range=temp_range,
            total_change=total_change,
            stage_changes=stage_changes,
            max_increase=max_increase,
            max_decrease=max_decrease,
            mean_change_rate=mean_change_rate
        )
    
    def _calculate_stage_changes(self, sequence: np.ndarray) -> List[float]:
        """
        Calculate temperature changes in five stages
        
        Args:
            sequence: Temperature sequence
            
        Returns:
            List of temperature changes for each stage
        """
        length = len(sequence)
        stage_boundaries = [
            0,
            int(0.2 * length),  # 20%
            int(0.4 * length),  # 40%
            int(0.6 * length),  # 60%
            int(0.8 * length),  # 80%
            length - 1          # 100%
        ]
        
        stage_changes = []
        for i in range(5):
            start_idx = stage_boundaries[i]
            end_idx = stage_boundaries[i + 1]
            
            start_temp = sequence[start_idx]
            end_temp = sequence[end_idx]
            change = float(end_temp - start_temp)
            stage_changes.append(change)
        
        return stage_changes
    
    def _calculate_overall_statistics(self):
        """Calculate overall statistics across all samples"""
        if not self.sample_statistics:
            return
        
        # Extract values for each statistic
        initial_temps = [stats.initial_temp for stats in self.sample_statistics.values()]
        final_temps = [stats.final_temp for stats in self.sample_statistics.values()]
        peak_temps = [stats.peak_temp for stats in self.sample_statistics.values()]
        peak_positions = [stats.peak_position for stats in self.sample_statistics.values()]
        total_changes = [stats.total_change for stats in self.sample_statistics.values()]
        
        # Calculate StatRange for each
        self.overall_statistics = OverallStatistics(
            initial_temp=self._calculate_stat_range(initial_temps),
            final_temp=self._calculate_stat_range(final_temps),
            peak_temp=self._calculate_stat_range(peak_temps),
            peak_position=self._calculate_stat_range(peak_positions),
            total_change=self._calculate_stat_range(total_changes)
        )
    
    def _calculate_stat_range(self, values: List[float]) -> StatRange:
        """Calculate statistical range for a list of values"""
        values_array = np.array(values)
        return StatRange(
            mean=float(np.mean(values_array)),
            std=float(np.std(values_array)),
            min=float(np.min(values_array)),
            max=float(np.max(values_array))
        )
    
    def get_reference_template(self) -> np.ndarray:
        """
        Get reference template (Sample_1) for sequence generation
        
        Returns:
            Reference temperature sequence
        """
        if self.reference_template is not None:
            return self.reference_template
        
        if not self.sample_data:
            self.load_sample_data()
        
        if 'Sample_1' not in self.sample_data:
            raise ValueError("Sample_1 not found in loaded data")
        
        self.reference_template = self.sample_data['Sample_1'].copy()
        logger.info(f"Reference template loaded: {len(self.reference_template)} points")
        
        return self.reference_template
    
    def calculate_sequence_similarity(self, sequence: np.ndarray, 
                                    reference_sample: str = 'Sample_1') -> SimilarityMetrics:
        """
        Calculate similarity between a sequence and a reference sample
        
        Args:
            sequence: Input temperature sequence
            reference_sample: Name of reference sample (default: Sample_1)
            
        Returns:
            Comprehensive similarity metrics
        """
        if not self.sample_data:
            self.load_sample_data()
        
        if reference_sample not in self.sample_data:
            raise ValueError(f"Reference sample {reference_sample} not found")
        
        reference = self.sample_data[reference_sample]
        
        # Ensure sequences have same length for comparison
        min_length = min(len(sequence), len(reference))
        seq_trimmed = sequence[:min_length]
        ref_trimmed = reference[:min_length]
        
        # Calculate correlation
        correlation, _ = pearsonr(seq_trimmed, ref_trimmed)
        if np.isnan(correlation):
            correlation = 0.0
        
        # Calculate Euclidean similarity (normalized)
        euclidean_dist = euclidean(seq_trimmed, ref_trimmed)
        max_possible_dist = np.sqrt(np.sum((np.max(ref_trimmed) - np.min(ref_trimmed))**2 * len(ref_trimmed)))
        euclidean_similarity = 1.0 - (euclidean_dist / max_possible_dist) if max_possible_dist > 0 else 1.0
        
        # Calculate feature-based similarities
        seq_stats = self._calculate_single_sample_statistics(sequence)
        ref_stats = self.sample_statistics.get(reference_sample)
        
        if ref_stats is None:
            # Calculate reference statistics if not available
            ref_stats = self._calculate_single_sample_statistics(reference)
        
        # Feature similarities (normalized to [0, 1])
        initial_temp_similarity = self._calculate_feature_similarity(
            seq_stats.initial_temp, ref_stats.initial_temp, self.overall_statistics.initial_temp)
        
        final_temp_similarity = self._calculate_feature_similarity(
            seq_stats.final_temp, ref_stats.final_temp, self.overall_statistics.final_temp)
        
        peak_temp_similarity = self._calculate_feature_similarity(
            seq_stats.peak_temp, ref_stats.peak_temp, self.overall_statistics.peak_temp)
        
        peak_position_similarity = self._calculate_feature_similarity(
            seq_stats.peak_position, ref_stats.peak_position, self.overall_statistics.peak_position)
        
        # Overall similarity (weighted average)
        overall_similarity = (
            0.3 * correlation +
            0.2 * euclidean_similarity +
            0.15 * initial_temp_similarity +
            0.15 * final_temp_similarity +
            0.1 * peak_temp_similarity +
            0.1 * peak_position_similarity
        )
        
        return SimilarityMetrics(
            correlation=float(correlation),
            euclidean_similarity=float(euclidean_similarity),
            initial_temp_similarity=float(initial_temp_similarity),
            final_temp_similarity=float(final_temp_similarity),
            peak_temp_similarity=float(peak_temp_similarity),
            peak_position_similarity=float(peak_position_similarity),
            overall_similarity=float(overall_similarity)
        )
    
    def _calculate_feature_similarity(self, value1: float, value2: float, 
                                    stat_range: StatRange) -> float:
        """
        Calculate similarity between two feature values using statistical range
        
        Args:
            value1: First value
            value2: Second value
            stat_range: Statistical range for normalization
            
        Returns:
            Similarity score [0, 1]
        """
        if stat_range.max == stat_range.min:
            return 1.0 if value1 == value2 else 0.0
        
        # Normalize difference by the range
        normalized_diff = abs(value1 - value2) / (stat_range.max - stat_range.min)
        similarity = max(0.0, 1.0 - normalized_diff)
        
        return similarity
    
    def calculate_all_similarities(self, sequence: np.ndarray) -> Dict[str, float]:
        """
        Calculate similarity with all loaded samples
        
        Args:
            sequence: Input temperature sequence
            
        Returns:
            Dictionary mapping sample names to overall similarity scores
        """
        similarities = {}
        
        for sample_name in self.sample_data.keys():
            try:
                metrics = self.calculate_sequence_similarity(sequence, sample_name)
                similarities[sample_name] = metrics.overall_similarity
            except Exception as e:
                logger.error(f"Failed to calculate similarity with {sample_name}: {e}")
                similarities[sample_name] = 0.0
        
        return similarities
    
    def get_sample_summary(self) -> Dict:
        """
        Get comprehensive summary of all sample data
        
        Returns:
            Dictionary containing sample analysis summary
        """
        if not self.sample_statistics:
            self.calculate_sample_statistics()
        
        summary = {
            'total_samples': len(self.sample_data),
            'overall_statistics': {
                'initial_temp': {
                    'mean': self.overall_statistics.initial_temp.mean,
                    'std': self.overall_statistics.initial_temp.std,
                    'range': [self.overall_statistics.initial_temp.min, 
                             self.overall_statistics.initial_temp.max]
                },
                'final_temp': {
                    'mean': self.overall_statistics.final_temp.mean,
                    'std': self.overall_statistics.final_temp.std,
                    'range': [self.overall_statistics.final_temp.min, 
                             self.overall_statistics.final_temp.max]
                },
                'peak_temp': {
                    'mean': self.overall_statistics.peak_temp.mean,
                    'std': self.overall_statistics.peak_temp.std,
                    'range': [self.overall_statistics.peak_temp.min, 
                             self.overall_statistics.peak_temp.max]
                },
                'total_change': {
                    'mean': self.overall_statistics.total_change.mean,
                    'std': self.overall_statistics.total_change.std,
                    'range': [self.overall_statistics.total_change.min, 
                             self.overall_statistics.total_change.max]
                }
            },
            'stage_analysis': self._get_stage_analysis_summary()
        }
        
        return summary
    
    def _get_stage_analysis_summary(self) -> Dict:
        """Get summary of five-stage analysis across all samples"""
        if not self.sample_statistics:
            return {}
        
        # Collect stage changes from all samples
        all_stage_changes = [stats.stage_changes for stats in self.sample_statistics.values()]
        
        stage_summary = {}
        for stage_idx in range(5):
            stage_changes = [changes[stage_idx] for changes in all_stage_changes]
            stage_summary[f'stage_{stage_idx + 1}'] = {
                'mean_change': float(np.mean(stage_changes)),
                'std_change': float(np.std(stage_changes)),
                'range': [float(np.min(stage_changes)), float(np.max(stage_changes))]
            }
        
        return stage_summary
    