# 化工车间温度序列MOEA/D多目标优化系统

基于MOEA/D算法的化工车间温度序列多目标优化系统，同时优化多个质量指标找到最优温度序列。

## 🚀 项目概述

### 核心技术

- **MOEA/D多目标优化**：同时优化三个目标函数（label_1最小化、label_2最大化、平滑性最大化）
- **ResNet+GRU+PCA架构**：深度特征提取，有效处理长序列温度数据
- **基于分类学习的预测**：通过帕累托支配关系进行序列质量分类
- **傅里叶平滑性指标**：基于频域分析的工艺稳定性量化

### 项目结构

```
moead_optimization_model/
├── src/                          # 核心源代码模块
├── config/config.yaml            # 配置文件
├── data/Esterification/          # 酯化反应数据集（21个温度序列样本）
├── models/                       # 训练好的模型
├── results/                      # 优化结果
├── train_classifier.py          # 分类器训练
├── run_moead_optimization.py  # 主要优化算法
├── main.py                       # 主执行脚本
└── requirements.txt              # 依赖包列表
```

## 🛠️ 环境配置

### 依赖安装

```bash
# 激活化工优化专用环境
conda activate chemo

# 安装依赖
pip install -r requirements.txt
```

### 核心依赖

- **科学计算**：numpy, pandas, scipy
- **机器学习**：scikit-learn, torch
- **可视化**：matplotlib, seaborn

## 📊 数据说明

- **温度序列**：21个样本，每个18K-92K个数据点，温度范围13-152°C
- **质量指标**：label_1 (0.05-0.47，越小越好)、label_2 (86.4-99.16，越大越好)
- **优化目标**：同时优化三个目标函数（label_1最小化、label_2最大化、平滑性最大化）

## 🚀 快速开始

### 方式1：完整优化流程

```bash
# 激活环境
conda activate chemo

# 执行完整的训练+优化流程
python main.py --mode full --algorithm moead --save-plots
```

### 方式2：分步执行

```bash
# 步骤1: 训练分类器
python train_classifier.py --save-plots --use-resnet-gru

# 步骤2: 执行MOEA/D优化
python run_moead_optimization.py --save-plots
```

### 方式3：仅优化（使用预训练模型）

```bash
# 使用默认模型进行优化
python run_moead_optimization.py --save-plots
```

### 主要参数

- `--population-size 100`: 种群大小
- `--max-generations 200`: 最大代数
- `--objective-weights 1.0 1.0 1.0`: 三目标权重
- `--save-plots`: 保存可视化结果

## 📈 结果输出

### 模型文件 (`models/`)

- `feature_extractor.joblib`: ResNet+GRU特征提取器
- `sequence_classifier.joblib`: 分类预测器
- `*_metadata.joblib`: 模型元数据

### 结果文件 (`results/`)

- `moead_optimization_results_*.npy`: MOEA/D优化结果
- `pareto_front_*.npy`: Pareto前沿解集
- `best_temperature_sequences_*.npy`: 最优温度序列
- `*.png`: 可视化图表

## 🔧 故障排除

### 常见问题

1. **内存不足**: 减少种群大小 `--population-size 50`
2. **CUDA错误**: 禁用GPU `export CUDA_VISIBLE_DEVICES=""`
3. **依赖冲突**: 重新安装 `pip install --force-reinstall -r requirements.txt`

## 📄 许可证

本项目采用MIT许可证
