#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于Sample_1.xlsx真实数据的约束管理器
"""

import numpy as np
import json
import logging
from typing import Dict, List, Tuple, Optional

logger = logging.getLogger(__name__)

class RealDataConstraints:
    """基于真实温度数据的约束管理器"""
    
    def __init__(self, config_file: str = "temperature_constraints_optimized.json"):
        """
        初始化约束管理器

        Args:
            config_file: 约束配置文件路径
        """
        self.config_file = config_file
        self.constraints = self._load_constraints()
        
    def _load_constraints(self) -> Dict:
        """加载约束配置"""
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                constraints = json.load(f)
            logger.info(f"成功加载约束配置: {self.config_file}")
            return constraints
        except FileNotFoundError:
            logger.warning(f"约束配置文件未找到: {self.config_file}，使用默认配置")
            return self._get_default_constraints()
        except Exception as e:
            logger.error(f"加载约束配置失败: {e}，使用默认配置")
            return self._get_default_constraints()
    
    def _get_default_constraints(self) -> Dict:
        """获取默认约束配置（基于21个样本的全面分析）"""
        return {
            "initial_temperature": {
                "min": 15.0, "max": 50.0, "mean": 31.05, "std": 19.28,
                "distribution": "normal", "flexible": True,
                "description": "基于21个样本：范围13.5-102.1°C，平均31.05°C，允许灵活变化"
            },
            "peak_temperature": {
                "target": 151.3, "mean": 147.76, "std": 2.20,
                "min": 143.9, "max": 151.3,
                "position_ratio": {"mean": 0.958, "std": 0.025},
                "description": "峰值温度应达到147-151°C，位置在95.8%附近"
            },
            "final_temperature": {
                "min": 129.0, "max": 146.6, "mean": 140.44, "std": 4.13,
                "description": "基于21个样本：范围129.0-146.6°C，平均140.4°C"
            },
            "peak_temperature": {
                "min": 143.9, "max": 151.3, "center": 147.8, "std": 2.3,
                "target": 151.3, "position": 0.958,  # 峰值出现在95.8%位置
                "high_temp_duration": 0.128,  # 高温维持12.8%时长
                "description": "基于21个样本：范围143.9-151.3°C，平均147.8°C"
            },
            "total_change": {
                "min": 38.6, "max": 129.2, "center": 109.4, "std": 19.8,
                "description": "基于21个样本：范围38.6-129.2°C，平均109.4°C"
            },
            "post_peak_behavior": {
                "dominant_pattern": "cooling",  # 61.9%样本为降温模式
                "average_change": -7.3,  # 平均降温7.3°C
                "cooling_ratio": 0.619,
                "maintain_ratio": 0.381
            },
            "penalty_weights": {
                "initial_temp_violation": 2000.0,  # 提高惩罚权重
                "final_temp_violation": 2000.0,
                "peak_temp_violation": 3000.0,  # 峰值温度最重要
                "total_change_violation": 1000.0,
                "stage_pattern_violation": 500.0,
                "rate_violation": 200.0
            }
        }
    
    def calculate_constraint_penalty(self, temperature_sequence: np.ndarray) -> float:
        """
        计算约束违反惩罚
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            总惩罚值
        """
        if len(temperature_sequence) == 0:
            return 1000.0
        
        total_penalty = 0.0
        penalties = self.constraints.get("penalty_weights", {})
        
        # 1. 起始温度约束（基于正态分布的灵活约束）
        initial_temp = temperature_sequence[0]
        initial_constraints = self.constraints.get("initial_temperature", {})

        # 使用正态分布约束而非固定值
        mean = initial_constraints.get("mean", 31.05)
        std = initial_constraints.get("std", 19.28)
        min_val = initial_constraints.get("min", 15.0)
        max_val = initial_constraints.get("max", 50.0)

        # 计算基于正态分布的惩罚
        if initial_temp < min_val or initial_temp > max_val:
            # 超出合理范围的严厉惩罚
            if initial_temp < min_val:
                penalty = (min_val - initial_temp) ** 2 * penalties.get("initial_temp_violation", 2000.0)
            else:
                penalty = (initial_temp - max_val) ** 2 * penalties.get("initial_temp_violation", 2000.0)
            total_penalty += penalty
        else:
            # 在合理范围内，基于正态分布的软约束
            z_score = abs(initial_temp - mean) / std
            if z_score > 2.0:  # 超过2σ时给予轻微惩罚
                penalty = (z_score - 2.0) * 100.0
                total_penalty += penalty
        
        # 2. 最终温度约束（基于21个样本的统计）
        final_temp = temperature_sequence[-1]
        final_constraints = self.constraints.get("final_temperature", {})
        target_final = final_constraints.get("mean", 140.44)
        final_min = final_constraints.get("min", 129.0)
        final_max = final_constraints.get("max", 146.6)

        # 对偏离合理范围的情况进行惩罚
        if final_temp < final_min or final_temp > final_max:
            if final_temp < final_min:
                penalty = (final_min - final_temp) ** 2 * penalties.get("final_temp_violation", 2000.0)
            else:
                penalty = (final_temp - final_max) ** 2 * penalties.get("final_temp_violation", 2000.0)
            total_penalty += penalty
        
        # 3. 总体变化约束（强化惩罚）
        total_change = final_temp - initial_temp
        target_change = 126.4  # 真实数据总体变化
        change_deviation = abs(total_change - target_change)

        # 对偏离真实总体变化的情况进行严厉惩罚
        if change_deviation > 5.0:  # 允许5.0°C误差
            penalty = (change_deviation - 5.0) ** 2 * penalties.get("total_change_violation", 2000.0)
            total_penalty += penalty
        
        # 4. 峰值温度约束（新增）
        peak_temp = temperature_sequence.max()
        peak_constraints = self.constraints.get("peak_temperature", {})
        target_peak = peak_constraints.get("target", 151.3)
        peak_min = peak_constraints.get("min", 143.9)
        peak_max = peak_constraints.get("max", 151.3)

        # 峰值温度不足的惩罚
        if peak_temp < peak_min:
            penalty = (peak_min - peak_temp) ** 2 * penalties.get("peak_temp_violation", 3000.0)
            total_penalty += penalty
        elif peak_temp < target_peak:
            # 鼓励达到更高的峰值温度
            penalty = (target_peak - peak_temp) * penalties.get("peak_temp_violation", 1000.0)
            total_penalty += penalty

        # 检查峰值位置
        peak_idx = np.argmax(temperature_sequence)
        peak_position = peak_idx / len(temperature_sequence)
        expected_position = peak_constraints.get("position", 0.958)

        position_deviation = abs(peak_position - expected_position)
        if position_deviation > 0.1:  # 允许10%的位置偏差
            penalty = (position_deviation - 0.1) * 500.0
            total_penalty += penalty

        # 5. 阶段模式约束
        stage_penalty = self._calculate_stage_pattern_penalty(temperature_sequence)
        total_penalty += stage_penalty * penalties.get("stage_pattern_violation", 500.0)
        
        # 5. 变化率约束
        rate_penalty = self._calculate_rate_penalty(temperature_sequence)
        total_penalty += rate_penalty * penalties.get("rate_violation", 100.0)
        
        return total_penalty
    
    def _calculate_stage_pattern_penalty(self, temperature_sequence: np.ndarray) -> float:
        """
        计算阶段模式违反惩罚
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            阶段模式惩罚值
        """
        if len(temperature_sequence) < 5:
            return 100.0
        
        penalty = 0.0
        seq_length = len(temperature_sequence)
        
        # 基于21个样本真实数据的5阶段检查
        stage_boundaries = [
            int(seq_length * 0.2),   # 阶段1结束
            int(seq_length * 0.4),   # 阶段2结束
            int(seq_length * 0.6),   # 阶段3结束
            int(seq_length * 0.8),   # 阶段4结束
            seq_length - 1           # 阶段5结束
        ]

        # 阶段1：主要升温段（期望100.5°C变化）
        stage1_start = temperature_sequence[0]
        stage1_end = temperature_sequence[stage_boundaries[0]]
        stage1_change = stage1_end - stage1_start

        expected_stage1_change = 100.5
        if stage1_change < expected_stage1_change * 0.6:  # 至少60%的期望变化
            penalty += (expected_stage1_change * 0.6 - stage1_change) / 20.0

        # 阶段2：缓慢升温段（期望4.7°C变化）
        stage2_start = temperature_sequence[stage_boundaries[0]]
        stage2_end = temperature_sequence[stage_boundaries[1]]
        stage2_change = stage2_end - stage2_start

        # 阶段2应该是小幅变化
        if abs(stage2_change) > 15.0:  # 变化不应过大
            penalty += (abs(stage2_change) - 15.0) / 10.0

        # 阶段3：稳定段（期望-0.8°C变化）
        stage3_start = temperature_sequence[stage_boundaries[1]]
        stage3_end = temperature_sequence[stage_boundaries[2]]
        stage3_change = stage3_end - stage3_start

        # 阶段3应该相对稳定
        if abs(stage3_change) > 8.0:  # 基于真实数据范围
            penalty += (abs(stage3_change) - 8.0) / 5.0

        # 阶段4：轻微降温段（期望-2.0°C变化）
        stage4_start = temperature_sequence[stage_boundaries[2]]
        stage4_end = temperature_sequence[stage_boundaries[3]]
        stage4_change = stage4_end - stage4_start

        # 阶段4应该是轻微降温或稳定
        if stage4_change > 5.0:  # 不应该大幅升温
            penalty += (stage4_change - 5.0) / 5.0

        # 阶段5：最终升温段（期望6.9°C变化）
        stage5_start = temperature_sequence[stage_boundaries[3]]
        stage5_end = temperature_sequence[stage_boundaries[4]]
        stage5_change = stage5_end - stage5_start

        # 阶段5应该有适度升温
        if stage5_change < 0:  # 不应该降温
            penalty += abs(stage5_change) / 3.0

        # 检查整体趋势：基于真实数据平均109.4°C变化
        overall_change = temperature_sequence[-1] - temperature_sequence[0]
        if overall_change < 50.0:  # 最低要求
            penalty += (50.0 - overall_change) / 10.0
        
        return penalty
    
    def _calculate_rate_penalty(self, temperature_sequence: np.ndarray) -> float:
        """
        计算变化率违反惩罚
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            变化率惩罚值
        """
        if len(temperature_sequence) < 2:
            return 10.0
        
        penalty = 0.0
        
        # 计算变化率
        temp_diff = np.diff(temperature_sequence)
        
        # 基于真实数据的变化率约束
        rate_constraints = self.constraints.get("change_rate_constraints", {})
        max_heating_rate = rate_constraints.get("max_heating_rate", 0.4)
        max_cooling_rate = rate_constraints.get("max_cooling_rate", -0.2)
        
        # 检查过大的变化率
        excessive_heating = temp_diff[temp_diff > max_heating_rate]
        excessive_cooling = temp_diff[temp_diff < max_cooling_rate]
        
        # 对过大变化率进行惩罚
        if len(excessive_heating) > 0:
            penalty += np.sum(excessive_heating - max_heating_rate) * 10.0
        
        if len(excessive_cooling) > 0:
            penalty += np.sum(max_cooling_rate - excessive_cooling) * 10.0
        
        # 检查分布特征：真实数据中87.9%为恒温点
        stable_points = np.sum(np.abs(temp_diff) < 0.01)
        stable_ratio = stable_points / len(temp_diff)
        
        # 期望稳定点比例在80-90%之间
        if stable_ratio < 0.8:
            penalty += (0.8 - stable_ratio) * 50.0
        elif stable_ratio > 0.95:
            penalty += (stable_ratio - 0.95) * 20.0
        
        return penalty
    
    def apply_constraints(self, temperature_sequence: np.ndarray) -> np.ndarray:
        """
        应用约束条件修正温度序列（强化版）

        Args:
            temperature_sequence: 原始温度序列

        Returns:
            修正后的温度序列
        """
        corrected_sequence = temperature_sequence.copy()

        # 1. 灵活修正起始温度（基于正态分布）
        initial_constraints = self.constraints.get("initial_temperature", {})
        mean = initial_constraints.get("mean", 31.05)
        std = initial_constraints.get("std", 19.28)
        min_val = initial_constraints.get("min", 15.0)
        max_val = initial_constraints.get("max", 50.0)

        current_initial = corrected_sequence[0]
        if current_initial < min_val or current_initial > max_val:
            # 如果超出范围，采样一个合理的起始温度
            target_initial = np.clip(np.random.normal(mean, std), min_val, max_val)
            corrected_sequence[0] = target_initial

        # 2. 修正最终温度（基于真实数据范围）
        final_constraints = self.constraints.get("final_temperature", {})
        final_mean = final_constraints.get("mean", 140.44)
        final_std = final_constraints.get("std", 4.13)
        final_min = final_constraints.get("min", 129.0)
        final_max = final_constraints.get("max", 146.6)

        current_final = corrected_sequence[-1]
        if current_final < final_min or current_final > final_max:
            target_final = np.clip(np.random.normal(final_mean, final_std), final_min, final_max)
            corrected_sequence[-1] = target_final

        # 3. 确保峰值温度达到合理水平
        current_peak = corrected_sequence.max()
        peak_constraints = self.constraints.get("peak_temperature", {})
        target_peak = peak_constraints.get("target", 151.3)
        peak_min = peak_constraints.get("min", 143.9)

        if current_peak < peak_min:
            # 如果峰值过低，在95.8%位置设置合理的峰值
            peak_position = int(len(corrected_sequence) * 0.958)
            corrected_sequence[peak_position] = np.random.uniform(peak_min, target_peak)

        # 4. 应用5阶段模式约束（轻量级，不覆盖已生成的合理序列）
        # 只在序列明显不合理时才进行调整
        if self._needs_stage_pattern_correction(corrected_sequence):
            corrected_sequence = self._apply_five_stage_pattern(corrected_sequence)

        # 5. 平滑处理，保持约束
        corrected_sequence = self._smooth_with_constraints(corrected_sequence)

        return corrected_sequence

    def _needs_stage_pattern_correction(self, sequence: np.ndarray) -> bool:
        """
        判断序列是否需要阶段模式修正

        Args:
            sequence: 温度序列

        Returns:
            是否需要修正
        """
        if len(sequence) < 10:
            return False

        # 检查是否有明显的非单调性问题
        # 如果序列整体趋势合理，就不需要强制修正
        start_temp = sequence[0]
        end_temp = sequence[-1]
        peak_temp = sequence.max()

        # 基本合理性检查
        total_rise = end_temp - start_temp
        peak_rise = peak_temp - start_temp

        # 如果总升温在合理范围内，且有明显的峰值，则认为序列合理
        if 50 <= total_rise <= 150 and peak_rise >= total_rise * 0.8:
            return False

        return True

    def _apply_five_stage_pattern(self, sequence: np.ndarray) -> np.ndarray:
        """
        应用基于真实数据的5阶段温度模式

        Args:
            sequence: 输入序列

        Returns:
            应用5阶段模式后的序列
        """
        corrected = sequence.copy()
        seq_length = len(corrected)

        # 基于21个样本真实数据的5阶段定义（动态计算）
        current_initial = corrected[0]
        current_final = corrected[-1]

        # 根据真实数据统计，动态计算各阶段目标温度
        stage1_end = current_initial + 100.5  # 平均升温100.5°C
        stage2_end = stage1_end + 4.7         # 平均升温4.7°C
        stage3_end = stage2_end - 0.8         # 平均降温0.8°C
        stage4_end = stage3_end - 2.0         # 平均降温2.0°C
        stage5_end = current_final            # 最终温度

        stages = [
            {'ratio': 0.2, 'start_temp': current_initial, 'end_temp': stage1_end, 'pattern': 'major_heating'},
            {'ratio': 0.2, 'start_temp': stage1_end, 'end_temp': stage2_end, 'pattern': 'slow_heating'},
            {'ratio': 0.2, 'start_temp': stage2_end, 'end_temp': stage3_end, 'pattern': 'stable'},
            {'ratio': 0.2, 'start_temp': stage3_end, 'end_temp': stage4_end, 'pattern': 'slight_cooling'},
            {'ratio': 0.2, 'start_temp': stage4_end, 'end_temp': stage5_end, 'pattern': 'final_heating'}
        ]

        # 应用阶段约束
        current_pos = 0
        for i, stage in enumerate(stages):
            stage_length = int(seq_length * stage['ratio'])
            if i == len(stages) - 1:  # 最后阶段
                stage_length = seq_length - current_pos

            if stage_length > 0:
                end_pos = current_pos + stage_length

                # 为每个阶段生成理想的温度曲线
                stage_temps = np.linspace(stage['start_temp'], stage['end_temp'], stage_length)

                # 与原序列进行加权平均，保持一定的原始特征
                weight = 0.7  # 70%使用理想曲线，30%保持原始特征
                corrected[current_pos:end_pos] = (
                    weight * stage_temps +
                    (1 - weight) * corrected[current_pos:end_pos]
                )

                current_pos = end_pos

        # 确保端点约束
        corrected[0] = 16.4
        corrected[-1] = 142.8

        return corrected
    
    def _smooth_with_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """
        在保持约束的前提下平滑序列
        
        Args:
            sequence: 输入序列
            
        Returns:
            平滑后的序列
        """
        smoothed = sequence.copy()
        
        # 保持起始和结束温度不变
        start_temp = smoothed[0]
        end_temp = smoothed[-1]
        
        # 对中间部分进行轻微平滑
        if len(smoothed) > 10:
            # 使用移动平均，但保持端点
            window_size = min(5, len(smoothed) // 10)
            for i in range(window_size, len(smoothed) - window_size):
                window = smoothed[i-window_size:i+window_size+1]
                smoothed[i] = np.mean(window)
        
        # 恢复端点约束
        smoothed[0] = start_temp
        smoothed[-1] = end_temp
        
        return smoothed
    
    def validate_sequence(self, temperature_sequence: np.ndarray) -> Dict[str, bool]:
        """
        验证温度序列是否满足约束
        
        Args:
            temperature_sequence: 温度序列
            
        Returns:
            验证结果字典
        """
        validation = {}
        
        if len(temperature_sequence) == 0:
            return {"valid": False, "error": "空序列"}
        
        # 起始温度验证
        initial_temp = temperature_sequence[0]
        initial_constraints = self.constraints.get("initial_temperature", {})
        validation["initial_temp_valid"] = (
            initial_constraints.get("min", 15.0) <= initial_temp <= initial_constraints.get("max", 20.0)
        )
        
        # 最终温度验证
        final_temp = temperature_sequence[-1]
        final_constraints = self.constraints.get("final_temperature", {})
        validation["final_temp_valid"] = (
            final_constraints.get("min", 140.0) <= final_temp <= final_constraints.get("max", 145.0)
        )
        
        # 总体变化验证
        total_change = final_temp - initial_temp
        change_constraints = self.constraints.get("total_change", {})
        validation["total_change_valid"] = (
            change_constraints.get("min", 120.0) <= total_change <= change_constraints.get("max", 130.0)
        )
        
        # 整体趋势验证
        validation["trend_valid"] = total_change > 100.0  # 应该是显著的升温过程
        
        # 总体验证
        validation["overall_valid"] = all([
            validation["initial_temp_valid"],
            validation["final_temp_valid"], 
            validation["total_change_valid"],
            validation["trend_valid"]
        ])
        
        return validation
