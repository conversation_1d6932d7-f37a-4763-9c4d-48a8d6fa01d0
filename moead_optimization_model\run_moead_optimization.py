#!/usr/bin/env python3
"""
带温度约束的MOEA/D多目标优化执行脚本

该脚本在原有MOEA/D基础上添加了基于训练样本统计特性的温度约束
"""

import os
import sys
import argparse
import logging
import joblib
import numpy as np
import pandas as pd
import yaml
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from scipy.stats import pearsonr
from scipy.spatial.distance import euclidean

# 添加src目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.data_processor import DataProcessor
from src.moead_optimizer import MOEADOptimizer
from src.fitness_evaluator import MultiObjectiveFunctions
from src.sequence_generator import SequenceGenerator
from src.utils import load_config, setup_logging, create_directories, print_system_info, save_results
from src.real_data_constraints import RealDataConstraints

# Sample数据分析类
class SampleDataAnalyzer:
    """Sample数据分析器，用于提取真实数据的统计特征"""

    def __init__(self, data_dir: str = "data/Esterification"):
        """
        初始化Sample数据分析器

        Args:
            data_dir: Sample数据目录路径
        """
        self.data_dir = data_dir
        self.sample_data = {}
        self.sample_stats = {}
        self.reference_sample = None  # Sample_1作为参考

    def load_sample_data(self) -> Dict[str, np.ndarray]:
        """
        加载所有Sample数据

        Returns:
            Sample数据字典 {sample_name: temperature_sequence}
        """
        print("正在加载Sample数据...")

        sample_files = [f for f in os.listdir(self.data_dir) if f.startswith('Sample_') and f.endswith('.xlsx')]

        for file_name in sample_files:
            file_path = os.path.join(self.data_dir, file_name)
            try:
                df = pd.read_excel(file_path)
                # 使用第一列作为温度数据
                temp_data = df.iloc[:, 0].values
                sample_name = file_name.replace('.xlsx', '')
                self.sample_data[sample_name] = temp_data
                print(f"✓ 加载 {sample_name}: {len(temp_data)} 个数据点")
            except Exception as e:
                print(f"✗ 加载 {file_name} 失败: {e}")

        # 设置Sample_1为参考样本
        if 'Sample_1' in self.sample_data:
            self.reference_sample = self.sample_data['Sample_1']
            print(f"✓ 设置Sample_1为参考样本 ({len(self.reference_sample)} 个数据点)")

        print(f"总共加载了 {len(self.sample_data)} 个Sample文件")
        return self.sample_data

    def calculate_sample_statistics(self) -> Dict[str, Dict]:
        """
        计算所有Sample的统计特征

        Returns:
            统计特征字典
        """
        if not self.sample_data:
            self.load_sample_data()

        print("正在计算Sample统计特征...")

        for sample_name, temp_sequence in self.sample_data.items():
            stats = self._calculate_sequence_statistics(temp_sequence)
            self.sample_stats[sample_name] = stats

        # 计算整体统计特征
        self._calculate_overall_statistics()

        return self.sample_stats

    def _calculate_sequence_statistics(self, sequence: np.ndarray) -> Dict:
        """计算单个序列的统计特征"""
        stats = {
            # 基础统计
            'length': len(sequence),
            'initial_temp': sequence[0],
            'final_temp': sequence[-1],
            'min_temp': np.min(sequence),
            'max_temp': np.max(sequence),
            'mean_temp': np.mean(sequence),
            'std_temp': np.std(sequence),
            'temp_range': np.max(sequence) - np.min(sequence),
            'total_change': sequence[-1] - sequence[0],

            # 峰值特征
            'peak_temp': np.max(sequence),
            'peak_position': np.argmax(sequence) / len(sequence),
            'peak_index': np.argmax(sequence),

            # 变化率特征
            'temp_diff': np.diff(sequence),
            'max_increase': np.max(np.diff(sequence)),
            'max_decrease': np.min(np.diff(sequence)),
            'mean_change_rate': np.mean(np.abs(np.diff(sequence))),

            # 稳定性特征
            'stable_points_ratio': np.sum(np.abs(np.diff(sequence)) < 0.01) / (len(sequence) - 1),

            # 阶段特征（5阶段分析）
            'stage_changes': self._calculate_stage_changes(sequence)
        }

        return stats

    def _calculate_stage_changes(self, sequence: np.ndarray) -> List[float]:
        """计算5阶段的温度变化"""
        seq_length = len(sequence)
        stage_boundaries = [
            0,
            int(seq_length * 0.2),
            int(seq_length * 0.4),
            int(seq_length * 0.6),
            int(seq_length * 0.8),
            seq_length - 1
        ]

        stage_changes = []
        for i in range(5):
            start_idx = stage_boundaries[i]
            end_idx = stage_boundaries[i + 1]
            change = sequence[end_idx] - sequence[start_idx]
            stage_changes.append(change)

        return stage_changes

    def _calculate_overall_statistics(self):
        """计算所有Sample的整体统计特征"""
        if not self.sample_stats:
            return

        # 收集所有样本的关键指标
        initial_temps = [stats['initial_temp'] for stats in self.sample_stats.values()]
        final_temps = [stats['final_temp'] for stats in self.sample_stats.values()]
        peak_temps = [stats['peak_temp'] for stats in self.sample_stats.values()]
        peak_positions = [stats['peak_position'] for stats in self.sample_stats.values()]
        total_changes = [stats['total_change'] for stats in self.sample_stats.values()]

        # 计算整体统计
        self.overall_stats = {
            'initial_temp': {
                'mean': np.mean(initial_temps),
                'std': np.std(initial_temps),
                'min': np.min(initial_temps),
                'max': np.max(initial_temps)
            },
            'final_temp': {
                'mean': np.mean(final_temps),
                'std': np.std(final_temps),
                'min': np.min(final_temps),
                'max': np.max(final_temps)
            },
            'peak_temp': {
                'mean': np.mean(peak_temps),
                'std': np.std(peak_temps),
                'min': np.min(peak_temps),
                'max': np.max(peak_temps)
            },
            'peak_position': {
                'mean': np.mean(peak_positions),
                'std': np.std(peak_positions),
                'min': np.min(peak_positions),
                'max': np.max(peak_positions)
            },
            'total_change': {
                'mean': np.mean(total_changes),
                'std': np.std(total_changes),
                'min': np.min(total_changes),
                'max': np.max(total_changes)
            }
        }

        print("✓ 整体统计特征计算完成")

    def get_reference_template(self) -> np.ndarray:
        """
        获取参考模板（Sample_1）

        Returns:
            参考温度序列
        """
        if self.reference_sample is None:
            if not self.sample_data:
                self.load_sample_data()
            if 'Sample_1' in self.sample_data:
                self.reference_sample = self.sample_data['Sample_1']

        return self.reference_sample

    def calculate_sequence_similarity(self, sequence: np.ndarray, reference: np.ndarray = None) -> Dict[str, float]:
        """
        计算序列与参考序列的相似度

        Args:
            sequence: 待评估序列
            reference: 参考序列（默认使用Sample_1）

        Returns:
            相似度指标字典
        """
        if reference is None:
            reference = self.get_reference_template()

        if reference is None:
            return {'error': 'No reference template available'}

        # 标准化序列长度
        if len(sequence) != len(reference):
            # 重采样到相同长度
            indices = np.linspace(0, len(sequence) - 1, len(reference)).astype(int)
            sequence_resampled = sequence[indices]
        else:
            sequence_resampled = sequence

        # 计算多种相似度指标
        similarity_metrics = {}

        try:
            # 1. 皮尔逊相关系数
            correlation, _ = pearsonr(sequence_resampled, reference)
            similarity_metrics['correlation'] = correlation if not np.isnan(correlation) else 0.0

            # 2. 欧几里得距离（标准化）
            euclidean_dist = euclidean(sequence_resampled, reference)
            max_possible_dist = euclidean(np.full_like(reference, np.min(reference)),
                                        np.full_like(reference, np.max(reference)))
            similarity_metrics['euclidean_similarity'] = 1.0 - (euclidean_dist / max_possible_dist)

            # 3. 统计特征相似度
            seq_stats = self._calculate_sequence_statistics(sequence_resampled)
            ref_stats = self._calculate_sequence_statistics(reference)

            # 关键特征相似度
            initial_temp_sim = 1.0 - abs(seq_stats['initial_temp'] - ref_stats['initial_temp']) / ref_stats['temp_range']
            final_temp_sim = 1.0 - abs(seq_stats['final_temp'] - ref_stats['final_temp']) / ref_stats['temp_range']
            peak_temp_sim = 1.0 - abs(seq_stats['peak_temp'] - ref_stats['peak_temp']) / ref_stats['temp_range']
            peak_pos_sim = 1.0 - abs(seq_stats['peak_position'] - ref_stats['peak_position'])

            similarity_metrics['initial_temp_similarity'] = max(0.0, initial_temp_sim)
            similarity_metrics['final_temp_similarity'] = max(0.0, final_temp_sim)
            similarity_metrics['peak_temp_similarity'] = max(0.0, peak_temp_sim)
            similarity_metrics['peak_position_similarity'] = max(0.0, peak_pos_sim)

            # 4. 综合相似度
            similarity_metrics['overall_similarity'] = np.mean([
                similarity_metrics['correlation'],
                similarity_metrics['euclidean_similarity'],
                similarity_metrics['initial_temp_similarity'],
                similarity_metrics['final_temp_similarity'],
                similarity_metrics['peak_temp_similarity'],
                similarity_metrics['peak_position_similarity']
            ])

        except Exception as e:
            print(f"计算相似度时出错: {e}")
            similarity_metrics['error'] = str(e)

        return similarity_metrics

# 导入温度约束函数
def load_temperature_constraints():
    """加载温度约束配置"""
    try:
        with open('temperature_constraints.json', 'r', encoding='utf-8') as f:
            constraints = json.load(f)
        return constraints
    except FileNotFoundError:
        print("警告: 未找到温度约束配置文件，将使用默认约束")
        return None

def apply_temperature_constraints(temperature_sequence, constraints=None):
    """
    应用基于训练样本统计特性的温度约束
    
    Args:
        temperature_sequence: 温度序列数组
        constraints: 约束配置字典
        
    Returns:
        constraint_violations: 约束违反程度列表
    """
    if constraints is None:
        # 默认约束（基于之前的分析结果）
        constraints = {
            'temperature_bounds': {'min': 13.1, 'max': 151.3},
            'initial_temperature': {'min': 11.8, 'max': 50.3},
            'final_temperature': {'min': 132.2, 'max': 148.7},
            'average_temperature': {'min': 130.4, 'max': 134.2},
            'temperature_range': {'min': 95.3, 'max': 142.3}
        }
    
    violations = []
    
    # 1. 温度范围约束
    temp_min = constraints['temperature_bounds']['min']
    temp_max = constraints['temperature_bounds']['max']
    for temp in temperature_sequence:
        if temp < temp_min:
            violations.append(temp_min - temp)
        elif temp > temp_max:
            violations.append(temp - temp_max)
    
    # 2. 起始温度约束
    initial_temp = temperature_sequence[0]
    initial_min = constraints['initial_temperature']['min']
    initial_max = constraints['initial_temperature']['max']
    if initial_temp < initial_min:
        violations.append(initial_min - initial_temp)
    elif initial_temp > initial_max:
        violations.append(initial_temp - initial_max)
    
    # 3. 终止温度约束
    final_temp = temperature_sequence[-1]
    final_min = constraints['final_temperature']['min']
    final_max = constraints['final_temperature']['max']
    if final_temp < final_min:
        violations.append(final_min - final_temp)
    elif final_temp > final_max:
        violations.append(final_temp - final_max)
    
    # 4. 平均温度约束
    mean_temp = np.mean(temperature_sequence)
    mean_min = constraints['average_temperature']['min']
    mean_max = constraints['average_temperature']['max']
    if mean_temp < mean_min:
        violations.append(mean_min - mean_temp)
    elif mean_temp > mean_max:
        violations.append(mean_temp - mean_max)
    
    # 5. 温度变化范围约束
    temp_range = np.max(temperature_sequence) - np.min(temperature_sequence)
    range_min = constraints['temperature_range']['min']
    range_max = constraints['temperature_range']['max']
    if temp_range < range_min:
        violations.append(range_min - temp_range)
    elif temp_range > range_max:
        violations.append(temp_range - range_max)
    
    return violations

def calculate_constraint_penalty(temperature_sequence, constraints=None, penalty_weight=1000.0):
    """
    计算约束违反的惩罚值（使用真实数据约束）

    Args:
        temperature_sequence: 温度序列数组
        constraints: 约束配置字典（兼容性保留，实际使用RealDataConstraints）
        penalty_weight: 惩罚权重（兼容性保留）

    Returns:
        penalty: 总惩罚值
    """
    # 使用基于真实数据的约束管理器
    real_constraints = RealDataConstraints()
    return real_constraints.calculate_constraint_penalty(temperature_sequence)

class SampleDataDrivenObjectiveFunctions:
    """基于Sample数据的目标函数类"""

    def __init__(self, sample_analyzer: SampleDataAnalyzer, constraints=None, penalty_weight=1000.0):
        """
        初始化基于Sample数据的目标函数

        Args:
            sample_analyzer: Sample数据分析器
            constraints: 约束配置
            penalty_weight: 惩罚权重
        """
        self.sample_analyzer = sample_analyzer
        self.constraints = constraints
        self.penalty_weight = penalty_weight

        # 确保Sample数据已加载
        if not self.sample_analyzer.sample_data:
            self.sample_analyzer.load_sample_data()
        if not self.sample_analyzer.sample_stats:
            self.sample_analyzer.calculate_sample_statistics()

        # 获取参考统计特征
        self.reference_stats = self.sample_analyzer.overall_stats
        self.reference_template = self.sample_analyzer.get_reference_template()

        print("✓ 基于Sample数据的目标函数初始化完成")

    def objective_1_statistical_deviation(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数1：最小化与Sample数据统计特征的偏差

        Args:
            temperature_sequence: 温度序列

        Returns:
            统计偏差值（越小越好）
        """
        try:
            # 计算当前序列的统计特征
            current_stats = self.sample_analyzer._calculate_sequence_statistics(temperature_sequence)

            # 计算与参考统计的偏差
            deviations = []

            # 1. 起始温度偏差（权重：0.2）
            initial_target = self.reference_stats['initial_temp']['mean']  # 约31.05°C
            initial_deviation = abs(current_stats['initial_temp'] - initial_target) / initial_target
            deviations.append(0.2 * initial_deviation)

            # 2. 峰值温度偏差（权重：0.3）
            peak_target = self.reference_stats['peak_temp']['mean']  # 约147.8°C
            peak_deviation = abs(current_stats['peak_temp'] - peak_target) / peak_target
            deviations.append(0.3 * peak_deviation)

            # 3. 峰值位置偏差（权重：0.2）
            peak_pos_target = self.reference_stats['peak_position']['mean']  # 约0.958
            peak_pos_deviation = abs(current_stats['peak_position'] - peak_pos_target)
            deviations.append(0.2 * peak_pos_deviation)

            # 4. 总升温偏差（权重：0.15）
            total_change_target = self.reference_stats['total_change']['mean']  # 约109.4°C
            total_change_deviation = abs(current_stats['total_change'] - total_change_target) / total_change_target
            deviations.append(0.15 * total_change_deviation)

            # 5. 最终温度偏差（权重：0.15）
            final_target = self.reference_stats['final_temp']['mean']  # 约140.4°C
            final_deviation = abs(current_stats['final_temp'] - final_target) / final_target
            deviations.append(0.15 * final_deviation)

            # 总偏差
            total_deviation = sum(deviations)

            return float(total_deviation)

        except Exception as e:
            print(f"目标函数1计算失败: {e}")
            return 10.0  # 返回较大的偏差值

    def objective_2_pattern_matching(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数2：最小化与Sample_1模式的差异

        Args:
            temperature_sequence: 温度序列

        Returns:
            模式差异值（越小越好）
        """
        try:
            # 计算与参考模板的相似度
            similarity_metrics = self.sample_analyzer.calculate_sequence_similarity(
                temperature_sequence, self.reference_template
            )

            # 转换为差异值（1 - 相似度）
            pattern_difference = 1.0 - similarity_metrics.get('overall_similarity', 0.0)

            return float(pattern_difference)

        except Exception as e:
            print(f"目标函数2计算失败: {e}")
            return 1.0  # 返回最大差异值

    def objective_3_stage_pattern_compliance(self, temperature_sequence: np.ndarray) -> float:
        """
        目标函数3：最小化5阶段模式违反程度

        Args:
            temperature_sequence: 温度序列

        Returns:
            阶段模式违反程度（越小越好）
        """
        try:
            # 计算当前序列的阶段变化
            current_stage_changes = self.sample_analyzer._calculate_stage_changes(temperature_sequence)

            # Sample_1的阶段变化模式（基于分析结果）
            # 阶段1: 主升温 (~100.5°C), 阶段2: 缓升温 (~4.7°C), 阶段3: 稳定 (~-0.8°C)
            # 阶段4: 轻降温 (~-2.0°C), 阶段5: 最终升温 (~6.9°C)
            target_stage_changes = [100.5, 4.7, -0.8, -2.0, 6.9]

            # 计算阶段模式违反程度
            violations = []

            for i, (current, target) in enumerate(zip(current_stage_changes, target_stage_changes)):
                if i == 0:  # 阶段1：主升温段，应该有显著升温
                    if current < target * 0.6:  # 至少60%的期望升温
                        violations.append((target * 0.6 - current) / target)
                elif i == 1:  # 阶段2：缓升温段，变化应该较小
                    if abs(current) > 15.0:  # 变化不应过大
                        violations.append((abs(current) - 15.0) / 15.0)
                elif i == 2:  # 阶段3：稳定段，变化应该很小
                    if abs(current) > 8.0:
                        violations.append((abs(current) - 8.0) / 8.0)
                elif i == 3:  # 阶段4：轻降温段，不应大幅升温
                    if current > 5.0:
                        violations.append((current - 5.0) / 5.0)
                elif i == 4:  # 阶段5：最终升温段，不应降温
                    if current < 0:
                        violations.append(abs(current) / 10.0)

            # 总违反程度
            total_violation = sum(violations) if violations else 0.0

            return float(total_violation)

        except Exception as e:
            print(f"目标函数3计算失败: {e}")
            return 5.0  # 返回较大的违反程度

    def evaluate(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        评估所有目标函数

        Args:
            temperature_sequence: 温度序列

        Returns:
            包含目标函数值和约束惩罚的字典
        """
        # 计算三个数据驱动的目标函数
        f1_stat_dev = self.objective_1_statistical_deviation(temperature_sequence)
        f2_pattern_diff = self.objective_2_pattern_matching(temperature_sequence)
        f3_stage_violation = self.objective_3_stage_pattern_compliance(temperature_sequence)

        # 计算约束惩罚
        penalty = calculate_constraint_penalty(temperature_sequence, self.constraints, self.penalty_weight)

        # 构建目标函数字典（所有目标都是最小化）
        objectives = {
            'f1_statistical_deviation': f1_stat_dev + penalty * 0.1,  # 统计偏差 + 轻微惩罚
            'f2_pattern_difference': f2_pattern_diff + penalty * 0.1,  # 模式差异 + 轻微惩罚
            'f3_stage_violation': f3_stage_violation + penalty * 0.1,  # 阶段违反 + 轻微惩罚
            'constraint_penalty': penalty
        }

        return objectives

class ConstrainedMultiObjectiveFunctions(MultiObjectiveFunctions):
    """带约束的多目标函数类（保留兼容性）"""

    def __init__(self, classifier, feature_extractor, constraints=None, penalty_weight=1000.0):
        super().__init__(classifier, feature_extractor)
        self.constraints = constraints
        self.penalty_weight = penalty_weight

    def evaluate(self, temperature_sequence: np.ndarray) -> Dict[str, float]:
        """
        评估带约束的多目标函数

        Args:
            temperature_sequence: 温度序列

        Returns:
            包含目标函数值和约束惩罚的字典
        """
        # 计算原始目标函数值
        f1, f2, f3, f4 = super().evaluate_all_objectives(temperature_sequence)

        # 计算约束惩罚
        penalty = calculate_constraint_penalty(temperature_sequence, self.constraints, self.penalty_weight)

        # 将惩罚添加到目标函数中（最小化问题）
        objectives = {
            'f1_label1': f1 + penalty,
            'f2_label2': f2 + penalty,  # f2是负值，所以加惩罚会使其更差
            'f3_smoothness': f3 + penalty,  # f3是负值，所以加惩罚会使其更差
            'constraint_penalty': penalty
        }

        return objectives

class SampleBasedSequenceGenerator:
    """基于Sample数据的序列生成器"""

    def __init__(self, sample_analyzer: SampleDataAnalyzer, noise_level: float = 0.05, template_weight: float = 0.7):
        """
        初始化基于Sample数据的序列生成器

        Args:
            sample_analyzer: Sample数据分析器
            noise_level: 噪声水平（0-1）
            template_weight: 模板权重（0-1，越高越接近原始模板）
        """
        self.sample_analyzer = sample_analyzer
        self.noise_level = noise_level
        self.template_weight = template_weight

        # 获取参考模板和统计特征
        self.reference_template = sample_analyzer.get_reference_template()
        self.reference_stats = sample_analyzer.overall_stats

        # 序列参数
        self.sequence_length = len(self.reference_template)
        self.min_temp = 13.1  # 基于所有Sample的最小值
        self.max_temp = 151.3  # 基于所有Sample的最大值

        print(f"✓ 基于Sample数据的序列生成器初始化完成")
        print(f"  - 参考模板长度: {self.sequence_length}")
        print(f"  - 噪声水平: {self.noise_level}")
        print(f"  - 模板权重: {self.template_weight}")

    def generate_sample_based_sequence(self, seed: Optional[int] = None) -> np.ndarray:
        """
        生成基于Sample数据的温度序列

        Args:
            seed: 随机种子

        Returns:
            基于Sample模板的温度序列
        """
        if seed is not None:
            np.random.seed(seed)

        # 1. 从参考模板开始
        base_sequence = self.reference_template.copy()

        # 2. 添加随机扰动以产生多样性
        noise = np.random.normal(0, self.noise_level * np.std(base_sequence), len(base_sequence))

        # 3. 生成随机变体
        random_sequence = self._generate_random_variant()

        # 4. 加权组合
        generated_sequence = (
            self.template_weight * base_sequence +
            (1 - self.template_weight) * random_sequence +
            noise
        )

        # 5. 应用约束确保合理性
        generated_sequence = self._apply_sample_constraints(generated_sequence)

        return generated_sequence

    def _generate_random_variant(self) -> np.ndarray:
        """生成随机变体序列"""
        # 基于Sample统计特征生成随机序列
        initial_temp = np.random.normal(
            self.reference_stats['initial_temp']['mean'],
            self.reference_stats['initial_temp']['std']
        )

        final_temp = np.random.normal(
            self.reference_stats['final_temp']['mean'],
            self.reference_stats['final_temp']['std']
        )

        peak_temp = np.random.normal(
            self.reference_stats['peak_temp']['mean'],
            self.reference_stats['peak_temp']['std']
        )

        peak_position = np.random.normal(
            self.reference_stats['peak_position']['mean'],
            self.reference_stats['peak_position']['std']
        )

        # 确保合理范围
        initial_temp = np.clip(initial_temp, self.min_temp, 50.0)
        final_temp = np.clip(final_temp, 120.0, self.max_temp)
        peak_temp = np.clip(peak_temp, 140.0, self.max_temp)
        peak_position = np.clip(peak_position, 0.85, 0.99)

        # 生成5阶段序列
        return self._generate_five_stage_sequence(initial_temp, final_temp, peak_temp, peak_position)

    def _generate_five_stage_sequence(self, initial_temp: float, final_temp: float,
                                    peak_temp: float, peak_position: float) -> np.ndarray:
        """生成5阶段温度序列"""
        sequence = np.zeros(self.sequence_length)

        # 计算阶段边界
        stage_boundaries = [
            0,
            int(self.sequence_length * 0.2),
            int(self.sequence_length * 0.4),
            int(self.sequence_length * 0.6),
            int(self.sequence_length * 0.8),
            self.sequence_length - 1
        ]

        peak_index = int(peak_position * self.sequence_length)

        # 阶段1：主升温段 (0-20%)
        stage1_end_temp = initial_temp + 100.5  # 基于Sample分析的平均升温
        sequence[stage_boundaries[0]:stage_boundaries[1]] = np.linspace(
            initial_temp, stage1_end_temp, stage_boundaries[1] - stage_boundaries[0]
        )

        # 阶段2：缓升温段 (20-40%)
        stage2_end_temp = stage1_end_temp + 4.7  # 基于Sample分析
        sequence[stage_boundaries[1]:stage_boundaries[2]] = np.linspace(
            stage1_end_temp, stage2_end_temp, stage_boundaries[2] - stage_boundaries[1]
        )

        # 阶段3：稳定段 (40-60%)
        stage3_end_temp = stage2_end_temp - 0.8  # 基于Sample分析
        sequence[stage_boundaries[2]:stage_boundaries[3]] = np.linspace(
            stage2_end_temp, stage3_end_temp, stage_boundaries[3] - stage_boundaries[2]
        )

        # 阶段4：轻降温段 (60-80%)
        stage4_end_temp = stage3_end_temp - 2.0  # 基于Sample分析
        sequence[stage_boundaries[3]:stage_boundaries[4]] = np.linspace(
            stage3_end_temp, stage4_end_temp, stage_boundaries[4] - stage_boundaries[3]
        )

        # 阶段5：最终升温段 (80-100%)
        # 在这个阶段达到峰值，然后降到最终温度
        if peak_index >= stage_boundaries[4]:
            # 峰值在阶段5
            sequence[stage_boundaries[4]:peak_index] = np.linspace(
                stage4_end_temp, peak_temp, peak_index - stage_boundaries[4]
            )
            sequence[peak_index:] = np.linspace(
                peak_temp, final_temp, len(sequence) - peak_index
            )
        else:
            # 峰值在之前的阶段，直接升到最终温度
            sequence[stage_boundaries[4]:] = np.linspace(
                stage4_end_temp, final_temp, len(sequence) - stage_boundaries[4]
            )

        return sequence

    def _apply_sample_constraints(self, sequence: np.ndarray) -> np.ndarray:
        """应用基于Sample数据的约束"""
        # 1. 温度范围约束
        sequence = np.clip(sequence, self.min_temp, self.max_temp)

        # 2. 确保起始和结束温度在合理范围内
        initial_range = [13.5, 50.0]  # 基于Sample分析
        final_range = [129.0, 146.6]  # 基于Sample分析

        if sequence[0] < initial_range[0] or sequence[0] > initial_range[1]:
            sequence[0] = np.random.uniform(initial_range[0], initial_range[1])

        if sequence[-1] < final_range[0] or sequence[-1] > final_range[1]:
            sequence[-1] = np.random.uniform(final_range[0], final_range[1])

        # 3. 确保峰值在合理位置和范围
        peak_idx = np.argmax(sequence)
        peak_position = peak_idx / len(sequence)

        if peak_position < 0.85 or peak_position > 0.99:
            # 重新设置峰值位置
            new_peak_idx = int(np.random.uniform(0.85, 0.99) * len(sequence))
            peak_temp = sequence[peak_idx]
            sequence[new_peak_idx] = peak_temp
            sequence[peak_idx] = (sequence[peak_idx-1] + sequence[peak_idx+1]) / 2 if 0 < peak_idx < len(sequence)-1 else peak_temp * 0.9

        # 4. 平滑处理
        sequence = self._smooth_sequence(sequence)

        return sequence

    def _smooth_sequence(self, sequence: np.ndarray) -> np.ndarray:
        """轻微平滑序列，保持主要特征"""
        # 使用移动平均进行轻微平滑
        window_size = 5
        smoothed = sequence.copy()

        for i in range(window_size, len(sequence) - window_size):
            smoothed[i] = np.mean(sequence[i-window_size:i+window_size+1])

        # 保持端点不变
        smoothed[0] = sequence[0]
        smoothed[-1] = sequence[-1]

        return smoothed

    def generate_population(self, population_size: int) -> List[np.ndarray]:
        """
        生成基于Sample数据的初始种群

        Args:
            population_size: 种群大小

        Returns:
            温度序列种群
        """
        population = []

        for i in range(population_size):
            # 使用不同的随机种子生成多样性
            sequence = self.generate_sample_based_sequence(seed=i)
            population.append(sequence)

        print(f"✓ 生成了 {population_size} 个基于Sample数据的初始序列")
        return population

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='执行带温度约束的MOEA/D多目标优化')
    
    parser.add_argument('--config', type=str, default='config/config.yaml',
                       help='配置文件路径')
    parser.add_argument('--model-dir', type=str, default='models',
                       help='模型目录路径')
    parser.add_argument('--output-dir', type=str, default='results',
                       help='结果输出目录')
    
    # MOEA/D算法参数
    parser.add_argument('--population-size', type=int, default=100,
                       help='种群大小')
    parser.add_argument('--max-generations', type=int, default=200,
                       help='最大代数')
    parser.add_argument('--neighbor-size', type=int, default=20,
                       help='邻域大小')
    parser.add_argument('--F', type=float, default=0.5,
                       help='差分进化缩放因子')
    parser.add_argument('--CR', type=float, default=0.9,
                       help='交叉概率')
    
    # 约束参数
    parser.add_argument('--penalty-weight', type=float, default=1000.0,
                       help='约束违反惩罚权重')
    parser.add_argument('--constraints-file', type=str, default='temperature_constraints.json',
                       help='温度约束配置文件路径')
    
    # 分析选项
    parser.add_argument('--save-plots', action='store_true',
                       help='保存优化过程和Pareto前沿图表')
    parser.add_argument('--pareto-analysis', action='store_true',
                       help='执行详细的Pareto前沿分析')
    parser.add_argument('--verbose', action='store_true',
                       help='详细输出模式')

    return parser.parse_args()

def load_trained_models(model_dir: str):
    """加载训练好的模型"""
    # 查找模型文件
    classifier_files = [f for f in os.listdir(model_dir) if 'classifier.joblib' in f]
    feature_extractor_files = [f for f in os.listdir(model_dir) if 'feature_extractor.joblib' in f]

    if not classifier_files:
        raise FileNotFoundError(f"在 {model_dir} 中未找到分类器文件")
    if not feature_extractor_files:
        raise FileNotFoundError(f"在 {model_dir} 中未找到特征提取器文件")

    # 使用找到的模型文件
    classifier_path = os.path.join(model_dir, classifier_files[0])
    feature_extractor_path = os.path.join(model_dir, feature_extractor_files[0])

    print(f"加载分类器: {classifier_path}")
    try:
        classifier = joblib.load(classifier_path)
    except Exception as e:
        print(f"分类器加载失败: {e}")
        raise

    print(f"加载特征提取器: {feature_extractor_path}")
    try:
        feature_extractor = joblib.load(feature_extractor_path)
    except Exception as e:
        print(f"特征提取器加载失败: {e}")
        print("这可能是因为PyTorch未安装或版本不兼容")
        print("请安装PyTorch: conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia")
        raise

    return classifier, feature_extractor

def main():
    """主函数"""
    args = parse_arguments()
    
    # 设置日志
    logging.basicConfig(
        level=logging.INFO if args.verbose else logging.WARNING,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("=== 带温度约束的MOEA/D多目标优化 ===")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 1. 初始化Sample数据分析器
    print("\n1. 初始化Sample数据分析器...")
    sample_analyzer = SampleDataAnalyzer()
    sample_analyzer.load_sample_data()
    sample_analyzer.calculate_sample_statistics()

    # 显示Sample数据统计信息
    reference_template = sample_analyzer.get_reference_template()
    print(f"✓ Sample数据分析完成:")
    print(f"  - 加载样本数: {len(sample_analyzer.sample_data)}")
    print(f"  - 参考样本: Sample_1 ({len(reference_template) if reference_template is not None else 0} 个数据点)")
    print(f"  - 起始温度均值: {sample_analyzer.overall_stats['initial_temp']['mean']:.1f}°C")
    print(f"  - 峰值温度均值: {sample_analyzer.overall_stats['peak_temp']['mean']:.1f}°C")
    print(f"  - 峰值位置均值: {sample_analyzer.overall_stats['peak_position']['mean']:.3f}")
    print(f"  - 总升温均值: {sample_analyzer.overall_stats['total_change']['mean']:.1f}°C")

    # 2. 加载温度约束
    print("\n2. 加载温度约束...")
    if os.path.exists(args.constraints_file):
        with open(args.constraints_file, 'r', encoding='utf-8') as f:
            constraints = json.load(f)
        print(f"成功加载约束配置: {args.constraints_file}")
        print(f"温度范围约束: {constraints['temperature_bounds']['min']:.1f}°C - {constraints['temperature_bounds']['max']:.1f}°C")
    else:
        constraints = None
        print("使用默认约束配置")

    # 3. 加载训练好的模型（可选，用于兼容性）
    print("\n3. 加载训练好的模型...")
    try:
        classifier, feature_extractor = load_trained_models(args.model_dir)
        print("模型加载成功")
    except Exception as e:
        print(f"模型加载失败: {e}")
        print("将使用基于Sample数据的目标函数，无需预训练模型")
        classifier, feature_extractor = None, None

    # 4. 创建基于Sample数据的多目标函数评估器
    print("\n4. 创建基于Sample数据的多目标函数评估器...")
    fitness_evaluator = SampleDataDrivenObjectiveFunctions(
        sample_analyzer=sample_analyzer,
        constraints=constraints,
        penalty_weight=args.penalty_weight
    )
    print(f"约束惩罚权重: {args.penalty_weight}")
    print("✓ 使用数据驱动的目标函数：")
    print("  - F1: 统计特征偏差最小化")
    print("  - F2: 模式匹配差异最小化")
    print("  - F3: 5阶段工艺违反最小化")
    
    # 5. 创建基于Sample数据的序列生成器
    print("\n5. 创建基于Sample数据的温度序列生成器...")
    sequence_generator = SampleBasedSequenceGenerator(sample_analyzer)

    # 验证序列生成器配置
    print(f"序列长度: {sequence_generator.sequence_length}")
    print(f"温度范围: [{sequence_generator.min_temp}, {sequence_generator.max_temp}]°C")
    print(f"参考模板: Sample_1 ({len(sequence_generator.reference_template)} 个数据点)")
    print(f"噪声水平: {sequence_generator.noise_level}")
    print(f"模板权重: {sequence_generator.template_weight}")
    
    # 设置MOEA/D优化器参数
    moead_params = {
        'population_size': args.population_size,
        'max_generations': args.max_generations,
        'neighbor_size': args.neighbor_size,
        'F': args.F,
        'CR': args.CR,
        'sequence_length': 24321,  # 修复：基于Sample_1.xlsx真实数据的序列长度
        'temperature_bounds': (
            constraints['temperature_bounds']['min'] if constraints else 13.1,
            constraints['temperature_bounds']['max'] if constraints else 151.3
        )
    }

    print(f"MOEA/D参数:")
    for key, value in moead_params.items():
        print(f"  {key}: {value}")

    # 创建MOEA/D优化器
    print("\n5. 创建MOEA/D优化器...")
    optimizer = MOEADOptimizer(config_path=args.config)

    # 设置适应度评估器和序列生成器
    optimizer.fitness_evaluator = fitness_evaluator
    optimizer.sequence_generator = sequence_generator

    # 覆盖配置参数
    optimizer.population_size = args.population_size
    optimizer.max_generations = args.max_generations
    optimizer.neighbor_size = args.neighbor_size
    optimizer.F = args.F
    optimizer.CR = args.CR
    
    # 执行优化
    print("\n6. 执行多目标优化...")
    print("这可能需要一些时间，请耐心等待...")

    # 创建目标函数
    def objective_function(temperature_sequence):
        """目标函数包装器（适配新的数据驱动目标函数）"""
        objectives = fitness_evaluator.evaluate(temperature_sequence)
        return [
            objectives['f1_statistical_deviation'],      # 统计特征偏差最小化
            objectives['f2_pattern_difference'],         # 模式匹配差异最小化
            objectives['f3_stage_violation'],            # 5阶段工艺违反最小化
            objectives.get('constraint_penalty', 0.0)    # 约束惩罚
        ]

    try:
        results = optimizer.optimize(objective_function)
        pareto_front = results.get('pareto_front', [])
        optimization_history = results.get('optimization_history', [])
        print(f"优化完成！找到 {len(pareto_front)} 个Pareto最优解")
    except Exception as e:
        print(f"优化过程中出现错误: {e}")
        return
    
    # 生成时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 保存结果
    print("\n7. 保存优化结果...")
    
    # 保存Pareto前沿
    pareto_data = []
    for i, solution in enumerate(pareto_front):
        pareto_data.append({
            'solution_id': i + 1,
            'f1_statistical_deviation': solution.get('f1_statistical_deviation', 0.0),
            'f2_pattern_difference': solution.get('f2_pattern_difference', 0.0),
            'f3_stage_violation': solution.get('f3_stage_violation', 0.0),
            'constraint_penalty': solution.get('constraint_penalty', 0.0),
            # 添加与Sample数据的相似度分析
            'sample_similarity': sample_analyzer.calculate_sequence_similarity(
                solution['temperature_sequence']
            ).get('overall_similarity', 0.0)
        })
    
    pareto_df = pd.DataFrame(pareto_data)
    pareto_csv_path = os.path.join(args.output_dir, f'constrained_pareto_front_{timestamp}.csv')
    pareto_df.to_csv(pareto_csv_path, index=False)
    print(f"Pareto前沿数据已保存: {pareto_csv_path}")
    
    # 保存最优温度序列
    for i, solution in enumerate(pareto_front[:5]):  # 保存前5个解
        temp_sequence = solution['temperature_sequence']
        
        # 计算与Sample数据的相似度
        similarity_metrics = sample_analyzer.calculate_sequence_similarity(temp_sequence)

        # 创建DataFrame
        df = pd.DataFrame({
            '时间点': range(len(temp_sequence)),
            '温度(°C)': temp_sequence,
            '时间(分钟)': np.arange(len(temp_sequence)) * 0.1,  # 假设每0.1分钟一个点
            'f1_statistical_deviation': solution.get('f1_statistical_deviation', 0.0),
            'f2_pattern_difference': solution.get('f2_pattern_difference', 0.0),
            'f3_stage_violation': solution.get('f3_stage_violation', 0.0),
            'constraint_penalty': solution.get('constraint_penalty', 0.0),
            'sample_similarity': similarity_metrics.get('overall_similarity', 0.0),
            'correlation_with_sample1': similarity_metrics.get('correlation', 0.0),
            'peak_temp_similarity': similarity_metrics.get('peak_temp_similarity', 0.0),
            'peak_position_similarity': similarity_metrics.get('peak_position_similarity', 0.0)
        })
        
        excel_path = os.path.join(args.output_dir, f'constrained_pareto_solution_{i+1}_{timestamp}.xlsx')
        df.to_excel(excel_path, index=False)
        print(f"解 {i+1} 已保存: {excel_path}")
    
    # 分析优化结果和Sample数据相似度
    print("\n8. 分析优化结果和Sample数据相似度...")
    constraint_violations = [sol.get('constraint_penalty', 0.0) for sol in pareto_front]
    feasible_solutions = sum(1 for penalty in constraint_violations if penalty == 0.0)

    # 计算相似度统计
    similarities = []
    statistical_deviations = []
    pattern_differences = []
    stage_violations = []

    for solution in pareto_front:
        temp_seq = solution['temperature_sequence']
        similarity = sample_analyzer.calculate_sequence_similarity(temp_seq)
        similarities.append(similarity.get('overall_similarity', 0.0))

        # 获取目标函数值
        statistical_deviations.append(solution.get('f1_statistical_deviation', 0.0))
        pattern_differences.append(solution.get('f2_pattern_difference', 0.0))
        stage_violations.append(solution.get('f3_stage_violation', 0.0))

    print(f"可行解数量: {feasible_solutions}/{len(pareto_front)}")
    print(f"平均约束惩罚: {np.mean(constraint_violations):.4f}")
    print(f"最大约束惩罚: {np.max(constraint_violations):.4f}")
    print(f"\n=== Sample数据相似度分析 ===")
    print(f"平均相似度: {np.mean(similarities):.4f}")
    print(f"最高相似度: {np.max(similarities):.4f}")
    print(f"最低相似度: {np.min(similarities):.4f}")
    print(f"\n=== 目标函数分析 ===")
    print(f"平均统计偏差: {np.mean(statistical_deviations):.4f}")
    print(f"平均模式差异: {np.mean(pattern_differences):.4f}")
    print(f"平均阶段违反: {np.mean(stage_violations):.4f}")

    # 找到最佳解（相似度最高的解）
    best_similarity_idx = np.argmax(similarities)
    best_solution = pareto_front[best_similarity_idx]
    print(f"\n=== 最佳解分析（相似度最高）===")
    print(f"解索引: {best_similarity_idx}")
    print(f"相似度: {similarities[best_similarity_idx]:.4f}")
    print(f"统计偏差: {statistical_deviations[best_similarity_idx]:.4f}")
    print(f"模式差异: {pattern_differences[best_similarity_idx]:.4f}")
    print(f"阶段违反: {stage_violations[best_similarity_idx]:.4f}")

    # 分析最佳解的温度特征
    best_temp_seq = best_solution['temperature_sequence']
    best_stats = sample_analyzer._calculate_sequence_statistics(best_temp_seq)
    reference_template = sample_analyzer.get_reference_template()
    ref_stats = sample_analyzer._calculate_sequence_statistics(reference_template)

    print(f"\n=== 最佳解与Sample_1对比 ===")
    print(f"起始温度: {best_stats['initial_temp']:.1f}°C vs {ref_stats['initial_temp']:.1f}°C")
    print(f"峰值温度: {best_stats['peak_temp']:.1f}°C vs {ref_stats['peak_temp']:.1f}°C")
    print(f"峰值位置: {best_stats['peak_position']:.3f} vs {ref_stats['peak_position']:.3f}")
    print(f"最终温度: {best_stats['final_temp']:.1f}°C vs {ref_stats['final_temp']:.1f}°C")
    print(f"总升温: {best_stats['total_change']:.1f}°C vs {ref_stats['total_change']:.1f}°C")
    
    if args.pareto_analysis:
        print("\n9. 执行详细的Pareto前沿分析...")
        analyze_pareto_solutions(pareto_front, constraints, args.output_dir, timestamp, sample_analyzer)
    
    print(f"\n优化完成！结果已保存到 {args.output_dir}")
    print(f"结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

def analyze_pareto_solutions(pareto_front, constraints, output_dir, timestamp, sample_analyzer):
    """分析Pareto解的温度特性和与Sample数据的相似度"""
    print("分析Pareto解的温度特性和Sample数据相似度...")

    analysis_results = []

    for i, solution in enumerate(pareto_front):
        temp_seq = solution['temperature_sequence']

        # 计算温度统计特性
        temp_stats = sample_analyzer._calculate_sequence_statistics(temp_seq)

        # 计算与Sample数据的相似度
        similarity_metrics = sample_analyzer.calculate_sequence_similarity(temp_seq)

        stats = {
            'solution_id': i + 1,
            'initial_temp': temp_stats['initial_temp'],
            'final_temp': temp_stats['final_temp'],
            'min_temp': temp_stats['min_temp'],
            'max_temp': temp_stats['max_temp'],
            'mean_temp': temp_stats['mean_temp'],
            'std_temp': temp_stats['std_temp'],
            'temp_range': temp_stats['temp_range'],
            'peak_temp': temp_stats['peak_temp'],
            'peak_position': temp_stats['peak_position'],
            'total_change': temp_stats['total_change'],

            # 新的目标函数值
            'f1_statistical_deviation': solution.get('f1_statistical_deviation', 0.0),
            'f2_pattern_difference': solution.get('f2_pattern_difference', 0.0),
            'f3_stage_violation': solution.get('f3_stage_violation', 0.0),
            'constraint_penalty': solution.get('constraint_penalty', 0.0),

            # Sample数据相似度指标
            'overall_similarity': similarity_metrics.get('overall_similarity', 0.0),
            'correlation_with_sample1': similarity_metrics.get('correlation', 0.0),
            'euclidean_similarity': similarity_metrics.get('euclidean_similarity', 0.0),
            'initial_temp_similarity': similarity_metrics.get('initial_temp_similarity', 0.0),
            'final_temp_similarity': similarity_metrics.get('final_temp_similarity', 0.0),
            'peak_temp_similarity': similarity_metrics.get('peak_temp_similarity', 0.0),
            'peak_position_similarity': similarity_metrics.get('peak_position_similarity', 0.0)
        }
        
        # 检查约束满足情况
        if constraints:
            stats['temp_bounds_satisfied'] = (
                constraints['temperature_bounds']['min'] <= stats['min_temp'] and
                stats['max_temp'] <= constraints['temperature_bounds']['max']
            )
            stats['initial_temp_satisfied'] = (
                constraints['initial_temperature']['min'] <= stats['initial_temp'] <= 
                constraints['initial_temperature']['max']
            )
            stats['final_temp_satisfied'] = (
                constraints['final_temperature']['min'] <= stats['final_temp'] <= 
                constraints['final_temperature']['max']
            )
            stats['mean_temp_satisfied'] = (
                constraints['average_temperature']['min'] <= stats['mean_temp'] <= 
                constraints['average_temperature']['max']
            )
        
        analysis_results.append(stats)
    
    # 保存分析结果
    analysis_df = pd.DataFrame(analysis_results)
    analysis_path = os.path.join(output_dir, f'constrained_pareto_analysis_{timestamp}.csv')
    analysis_df.to_csv(analysis_path, index=False)
    print(f"Pareto解分析结果已保存: {analysis_path}")
    
    # 输出统计摘要
    print("\n=== Pareto解温度特性摘要 ===")
    print(f"起始温度范围: {analysis_df['initial_temp'].min():.1f}°C - {analysis_df['initial_temp'].max():.1f}°C")
    print(f"终止温度范围: {analysis_df['final_temp'].min():.1f}°C - {analysis_df['final_temp'].max():.1f}°C")
    print(f"峰值温度范围: {analysis_df['peak_temp'].min():.1f}°C - {analysis_df['peak_temp'].max():.1f}°C")
    print(f"峰值位置范围: {analysis_df['peak_position'].min():.3f} - {analysis_df['peak_position'].max():.3f}")
    print(f"总升温范围: {analysis_df['total_change'].min():.1f}°C - {analysis_df['total_change'].max():.1f}°C")

    print(f"\n=== Sample数据相似度摘要 ===")
    print(f"整体相似度范围: {analysis_df['overall_similarity'].min():.4f} - {analysis_df['overall_similarity'].max():.4f}")
    print(f"平均整体相似度: {analysis_df['overall_similarity'].mean():.4f}")
    print(f"与Sample_1相关性范围: {analysis_df['correlation_with_sample1'].min():.4f} - {analysis_df['correlation_with_sample1'].max():.4f}")
    print(f"峰值温度相似度范围: {analysis_df['peak_temp_similarity'].min():.4f} - {analysis_df['peak_temp_similarity'].max():.4f}")
    print(f"峰值位置相似度范围: {analysis_df['peak_position_similarity'].min():.4f} - {analysis_df['peak_position_similarity'].max():.4f}")

    print(f"\n=== 目标函数值摘要 ===")
    print(f"统计偏差范围: {analysis_df['f1_statistical_deviation'].min():.4f} - {analysis_df['f1_statistical_deviation'].max():.4f}")
    print(f"模式差异范围: {analysis_df['f2_pattern_difference'].min():.4f} - {analysis_df['f2_pattern_difference'].max():.4f}")
    print(f"阶段违反范围: {analysis_df['f3_stage_violation'].min():.4f} - {analysis_df['f3_stage_violation'].max():.4f}")

    # 找到最优解
    best_similarity_idx = analysis_df['overall_similarity'].idxmax()
    best_solution_stats = analysis_df.loc[best_similarity_idx]

    print(f"\n=== 最优解详细信息（解{best_solution_stats['solution_id']}）===")
    print(f"整体相似度: {best_solution_stats['overall_similarity']:.4f}")
    print(f"起始温度: {best_solution_stats['initial_temp']:.1f}°C")
    print(f"峰值温度: {best_solution_stats['peak_temp']:.1f}°C (位置: {best_solution_stats['peak_position']:.3f})")
    print(f"最终温度: {best_solution_stats['final_temp']:.1f}°C")
    print(f"总升温: {best_solution_stats['total_change']:.1f}°C")
    print(f"统计偏差: {best_solution_stats['f1_statistical_deviation']:.4f}")
    print(f"模式差异: {best_solution_stats['f2_pattern_difference']:.4f}")
    print(f"阶段违反: {best_solution_stats['f3_stage_violation']:.4f}")

    if constraints:
        print(f"\n=== 约束满足情况 ===")
        if 'temp_bounds_satisfied' in analysis_df.columns:
            print(f"温度边界约束满足率: {analysis_df['temp_bounds_satisfied'].mean()*100:.1f}%")
            print(f"起始温度约束满足率: {analysis_df['initial_temp_satisfied'].mean()*100:.1f}%")
            print(f"终止温度约束满足率: {analysis_df['final_temp_satisfied'].mean()*100:.1f}%")
            print(f"平均温度约束满足率: {analysis_df['mean_temp_satisfied'].mean()*100:.1f}%")

if __name__ == "__main__":
    main()