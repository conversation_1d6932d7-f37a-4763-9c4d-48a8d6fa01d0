#!/usr/bin/env python3
"""
序列比较分类器模块

该模块负责：
1. 训练SVM分类器来比较两个温度序列的质量
2. 提供序列质量比较的预测接口
3. 模型评估和性能分析
"""

import numpy as np
import pandas as pd
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.preprocessing import StandardScaler
import joblib
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Optional
import logging
import yaml
import os

logger = logging.getLogger(__name__)


class SequenceClassifier:
    """序列比较分类器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        """
        初始化序列分类器
        
        Args:
            config_path: 配置文件路径
        """
        with open(config_path, 'r', encoding='utf-8') as f:
            self.config = yaml.safe_load(f)
        
        self.classifier_config = self.config['classifier']
        self.model_config = self.config['model']
        
        # 初始化SVM分类器
        svm_params = self.classifier_config['svm']
        self.classifier = SVC(
            kernel=svm_params['kernel'],
            C=svm_params['C'],
            gamma=svm_params['gamma'],
            probability=svm_params['probability'],
            random_state=self.classifier_config['training']['random_state']
        )
        
        # 训练历史
        self.training_history = {}
        self.is_trained = False
        self.feature_names = []
        
    def train(self, X: np.ndarray, y: np.ndarray, feature_names: List[str] = None) -> Dict:
        """
        训练分类器
        
        Args:
            X: 特征矩阵
            y: 标签数组
            feature_names: 特征名称列表
            
        Returns:
            训练结果字典
        """
        logger.info(f"开始训练分类器，数据形状: {X.shape}")
        
        # 保存特征名称
        if feature_names:
            self.feature_names = feature_names
        
        # 数据分割
        train_config = self.classifier_config['training']
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, 
            test_size=train_config['test_size'],
            random_state=train_config['random_state'],
            stratify=y
        )
        
        logger.info(f"训练集大小: {X_train.shape[0]}, 测试集大小: {X_test.shape[0]}")
        
        # 训练模型
        self.classifier.fit(X_train, y_train)
        
        # 预测
        y_train_pred = self.classifier.predict(X_train)
        y_test_pred = self.classifier.predict(X_test)
        y_train_proba = self.classifier.predict_proba(X_train)[:, 1]
        y_test_proba = self.classifier.predict_proba(X_test)[:, 1]
        
        # 交叉验证
        cv_scores = cross_val_score(
            self.classifier, X_train, y_train, 
            cv=train_config['cv_folds'], 
            scoring='accuracy'
        )
        
        # 计算评估指标
        training_results = {
            'train_accuracy': np.mean(y_train == y_train_pred),
            'test_accuracy': np.mean(y_test == y_test_pred),
            'cv_accuracy_mean': np.mean(cv_scores),
            'cv_accuracy_std': np.std(cv_scores),
            'train_auc': roc_auc_score(y_train, y_train_proba),
            'test_auc': roc_auc_score(y_test, y_test_proba),
            'train_classification_report': classification_report(y_train, y_train_pred, output_dict=True),
            'test_classification_report': classification_report(y_test, y_test_pred, output_dict=True),
            'confusion_matrix': confusion_matrix(y_test, y_test_pred).tolist(),
            'feature_count': X.shape[1],
            'sample_count': X.shape[0]
        }
        
        # 保存训练历史
        self.training_history = training_results
        self.is_trained = True
        
        # 打印结果
        logger.info(f"训练完成！")
        logger.info(f"训练准确率: {training_results['train_accuracy']:.4f}")
        logger.info(f"测试准确率: {training_results['test_accuracy']:.4f}")
        logger.info(f"交叉验证准确率: {training_results['cv_accuracy_mean']:.4f} ± {training_results['cv_accuracy_std']:.4f}")
        logger.info(f"测试AUC: {training_results['test_auc']:.4f}")
        
        return training_results
    
    def predict_comparison(self, sequence1: np.ndarray, sequence2: np.ndarray, 
                          feature_extractor) -> Dict:
        """
        预测两个序列的质量比较
        
        Args:
            sequence1: 第一个温度序列
            sequence2: 第二个温度序列
            feature_extractor: 特征提取器实例
            
        Returns:
            预测结果字典
        """
        if not self.is_trained:
            raise ValueError("分类器尚未训练")
        
        # 构造成对数据
        pair_data = [{
            'sequence_1': sequence1,
            'sequence_2': sequence2,
            'label': 0  # 占位符
        }]
        
        # 提取特征
        features = feature_extractor.transform(pair_data)
        
        # 预测
        prediction = self.classifier.predict(features)[0]
        probability = self.classifier.predict_proba(features)[0]
        
        result = {
            'prediction': int(prediction),
            'probability_seq1_better': float(probability[1]),
            'probability_seq2_better': float(probability[0]),
            'confidence': float(max(probability)),
            'interpretation': "序列1质量更好" if prediction == 1 else "序列2质量更好"
        }
        
        return result
    
    def evaluate_model(self, X: np.ndarray, y: np.ndarray) -> Dict:
        """
        评估模型性能
        
        Args:
            X: 特征矩阵
            y: 真实标签
            
        Returns:
            评估结果字典
        """
        if not self.is_trained:
            raise ValueError("分类器尚未训练")
        
        # 预测
        y_pred = self.classifier.predict(X)
        y_proba = self.classifier.predict_proba(X)[:, 1]
        
        # 计算指标
        evaluation_results = {
            'accuracy': np.mean(y == y_pred),
            'auc': roc_auc_score(y, y_proba),
            'classification_report': classification_report(y, y_pred, output_dict=True),
            'confusion_matrix': confusion_matrix(y, y_pred).tolist()
        }
        
        return evaluation_results
    
    def get_feature_importance(self) -> Dict[str, float]:
        """
        获取特征重要性（对于线性SVM）
        
        Returns:
            特征重要性字典
        """
        if not self.is_trained:
            raise ValueError("分类器尚未训练")
        
        if self.classifier.kernel == 'linear':
            # 线性SVM可以直接获取特征权重
            importance = np.abs(self.classifier.coef_[0])
            
            if self.feature_names:
                return dict(zip(self.feature_names, importance))
            else:
                return {f"feature_{i}": imp for i, imp in enumerate(importance)}
        else:
            logger.warning("非线性SVM无法直接获取特征重要性")
            return {}
    
    def save_model(self, model_dir: str = None):
        """
        保存训练好的模型
        
        Args:
            model_dir: 模型保存目录
        """
        if not self.is_trained:
            raise ValueError("分类器尚未训练")
        
        if model_dir is None:
            model_dir = self.model_config['save_dir']
        
        os.makedirs(model_dir, exist_ok=True)
        
        # 保存分类器
        classifier_path = os.path.join(model_dir, f"{self.model_config['classifier_name']}.joblib")
        joblib.dump(self.classifier, classifier_path)
        
        # 保存训练历史和元数据
        metadata = {
            'training_history': self.training_history,
            'feature_names': self.feature_names,
            'config': self.classifier_config
        }
        
        metadata_path = os.path.join(model_dir, f"{self.model_config['classifier_name']}_metadata.joblib")
        joblib.dump(metadata, metadata_path)
        
        logger.info(f"模型已保存到 {model_dir}")
    
    def load_model(self, model_dir: str = None):
        """
        加载训练好的模型
        
        Args:
            model_dir: 模型保存目录
        """
        if model_dir is None:
            model_dir = self.model_config['save_dir']
        
        # 加载分类器
        classifier_path = os.path.join(model_dir, f"{self.model_config['classifier_name']}.joblib")
        self.classifier = joblib.load(classifier_path)
        
        # 加载元数据
        metadata_path = os.path.join(model_dir, f"{self.model_config['classifier_name']}_metadata.joblib")
        metadata = joblib.load(metadata_path)
        
        self.training_history = metadata['training_history']
        self.feature_names = metadata['feature_names']
        self.is_trained = True
        
        logger.info(f"模型已从 {model_dir} 加载")
    
    def plot_training_results(self, save_path: str = None):
        """
        绘制训练结果图表
        
        Args:
            save_path: 图表保存路径
        """
        if not self.training_history:
            raise ValueError("没有训练历史数据")
        
        fig, axes = plt.subplots(2, 2, figsize=(12, 10))
        fig.suptitle('分类器训练结果', fontsize=16)
        
        # 准确率对比
        accuracies = [
            self.training_history['train_accuracy'],
            self.training_history['test_accuracy'],
            self.training_history['cv_accuracy_mean']
        ]
        labels = ['训练集', '测试集', '交叉验证']
        
        axes[0, 0].bar(labels, accuracies)
        axes[0, 0].set_title('准确率对比')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].set_ylim(0, 1)
        
        # 混淆矩阵
        cm = np.array(self.training_history['confusion_matrix'])
        sns.heatmap(cm, annot=True, fmt='d', ax=axes[0, 1])
        axes[0, 1].set_title('混淆矩阵')
        axes[0, 1].set_xlabel('预测标签')
        axes[0, 1].set_ylabel('真实标签')
        
        # AUC对比
        aucs = [
            self.training_history['train_auc'],
            self.training_history['test_auc']
        ]
        auc_labels = ['训练集', '测试集']
        
        axes[1, 0].bar(auc_labels, aucs)
        axes[1, 0].set_title('AUC对比')
        axes[1, 0].set_ylabel('AUC')
        axes[1, 0].set_ylim(0, 1)
        
        # 特征重要性（如果可用）
        feature_importance = self.get_feature_importance()
        if feature_importance:
            # 显示前10个最重要的特征
            sorted_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:10]
            features, importance = zip(*sorted_features)
            
            axes[1, 1].barh(range(len(features)), importance)
            axes[1, 1].set_yticks(range(len(features)))
            axes[1, 1].set_yticklabels(features)
            axes[1, 1].set_title('特征重要性 (Top 10)')
            axes[1, 1].set_xlabel('重要性')
        else:
            axes[1, 1].text(0.5, 0.5, '特征重要性不可用\n(非线性SVM)', 
                           ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('特征重要性')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            logger.info(f"训练结果图表已保存到 {save_path}")
        
        plt.show()


def main():
    """测试分类器功能"""
    # 创建测试数据
    np.random.seed(42)
    X = np.random.randn(100, 50)  # 100个样本，50个特征
    y = np.random.randint(0, 2, 100)  # 二分类标签
    
    # 测试分类器
    classifier = SequenceClassifier()
    results = classifier.train(X, y)
    
    print("训练结果:")
    print(f"测试准确率: {results['test_accuracy']:.4f}")
    print(f"测试AUC: {results['test_auc']:.4f}")


if __name__ == "__main__":
    main()
